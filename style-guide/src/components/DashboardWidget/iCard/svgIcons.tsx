import React from "react";

export const EnlargeIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="18"
      height="19"
      viewBox="0 0 18 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M16.6929 2.94933V7.23184H18V0.732666H11.5008V2.03974H15.7833L1.30707 16.516V12.2335H0V18.7327H6.49918V17.4256H2.21667L16.6929 2.94933Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const ExternalLinkIcon = (props: React.SVGAttributes<SVGElement>) => (
  <svg
    width="20"
    height="21"
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props} // Spread all props for flexibility
  >
    <path
      d="M15.3337 17.7987H4.66699C4.13656 17.7987 3.62785 17.588 3.25278 17.213C2.87771 16.8379 2.66699 16.3292 2.66699 15.7987V5.13208C2.66699 4.60165 2.87771 4.09294 3.25278 3.71787C3.62785 3.34279 4.13656 3.13208 4.66699 3.13208H8.66699C8.8438 3.13208 9.01337 3.20232 9.1384 3.32734C9.26342 3.45237 9.33366 3.62194 9.33366 3.79875C9.33366 3.97556 9.26342 4.14513 9.1384 4.27015C9.01337 4.39518 8.8438 4.46541 8.66699 4.46541H4.66699C4.49018 4.46541 4.32061 4.53565 4.19559 4.66068C4.07056 4.7857 4.00033 4.95527 4.00033 5.13208V15.7987C4.00033 15.9756 4.07056 16.1451 4.19559 16.2702C4.32061 16.3952 4.49018 16.4654 4.66699 16.4654H15.3337C15.5105 16.4654 15.68 16.3952 15.8051 16.2702C15.9301 16.1451 16.0003 15.9756 16.0003 15.7987V11.7987C16.0003 11.6219 16.0706 11.4524 16.1956 11.3273C16.3206 11.2023 16.4902 11.1321 16.667 11.1321C16.8438 11.1321 17.0134 11.2023 17.1384 11.3273C17.2634 11.4524 17.3337 11.6219 17.3337 11.7987V15.7987C17.3337 16.3292 17.1229 16.8379 16.7479 17.213C16.3728 17.588 15.8641 17.7987 15.3337 17.7987ZM8.66699 12.4654C8.57925 12.4659 8.49228 12.4491 8.41105 12.4159C8.32983 12.3828 8.25595 12.3339 8.19366 12.2721C8.13117 12.2101 8.08158 12.1364 8.04773 12.0551C8.01389 11.9739 7.99646 11.8868 7.99646 11.7987C7.99646 11.7107 8.01389 11.6236 8.04773 11.5424C8.08158 11.4611 8.13117 11.3874 8.19366 11.3254L15.0603 4.46541H12.667C12.4902 4.46541 12.3206 4.39518 12.1956 4.27015C12.0706 4.14513 12.0003 3.97556 12.0003 3.79875C12.0003 3.62194 12.0706 3.45237 12.1956 3.32734C12.3206 3.20232 12.4902 3.13208 12.667 3.13208H16.667C16.8438 3.13208 17.0134 3.20232 17.1384 3.32734C17.2634 3.45237 17.3337 3.62194 17.3337 3.79875V7.79875C17.3337 7.97556 17.2634 8.14513 17.1384 8.27015C17.0134 8.39518 16.8438 8.46541 16.667 8.46541C16.4902 8.46541 16.3206 8.39518 16.1956 8.27015C16.0706 8.14513 16.0003 7.97556 16.0003 7.79875V5.40541L9.14033 12.2721C9.07803 12.3339 9.00415 12.3828 8.92293 12.4159C8.8417 12.4491 8.75473 12.4659 8.66699 12.4654Z"
      fill="#1F4A70"
    />
  </svg>
);

export const InfoIcon = (props: React.SVGAttributes<SVGElement>) => (
  <svg
    width="48"
    height="48"
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M24 45C35.598 45 45 35.598 45 24C45 12.402 35.598 3 24 3C12.402 3 3 12.402 3 24C3 35.598 12.402 45 24 45Z"
      stroke="#1F4A70"
      strokeWidth="3"
    />
    <path
      d="M22.406 33.5V17.1364H25.5914V33.5H22.406ZM24.0146 14.6115C23.4607 14.6115 22.9848 14.4268 22.5871 14.0575C22.1965 13.6811 22.0012 13.2337 22.0012 12.7152C22.0012 12.1896 22.1965 11.7422 22.5871 11.3729C22.9848 10.9964 23.4607 10.8082 24.0146 10.8082C24.5686 10.8082 25.0409 10.9964 25.4316 11.3729C25.8293 11.7422 26.0281 12.1896 26.0281 12.7152C26.0281 13.2337 25.8293 13.6811 25.4316 14.0575C25.0409 14.4268 24.5686 14.6115 24.0146 14.6115Z"
      fill="#1F4A70"
    />
  </svg>
);

export const RaCheckedIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="20"
      height="19"
      viewBox="0 0 20 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect
        x="1"
        y="0.499939"
        width="18"
        height="18"
        rx="2"
        fill="#0091B8"
        stroke="#0091B8"
      />
      <path
        d="M14.5363 6.20876C14.2578 5.93033 13.8402 5.93033 13.5618 6.20876L8.34118 11.4294L6.18333 9.27151C5.9049 8.99308 5.48725 8.99308 5.20882 9.27151C4.93039 9.54994 4.93039 9.96759 5.20882 10.246L7.85392 12.8911C7.99314 13.0303 8.13235 13.0999 8.34118 13.0999C8.55 13.0999 8.68921 13.0303 8.82843 12.8911L14.5363 7.18327C14.8147 6.90484 14.8147 6.48719 14.5363 6.20876Z"
        fill="white"
      />
    </svg>
  );
};

export const RaUncheckedIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="1"
        y="1"
        width="18"
        height="18"
        rx="2"
        fill="white"
        stroke="#CCCCCC"
      />
    </svg>
  );
};

export const LayoutGrid = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="14"
      height="15"
      viewBox="0 0 14 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M12.8333 8.12155C13.4547 8.12155 13.998 8.62737 14 9.28822V13.566C14 14.1873 13.5005 14.7327 12.8333 14.7327H8.55556C7.93424 14.7327 7.39087 14.2265 7.38889 13.566V9.28822C7.38889 8.6669 7.88986 8.12155 8.55556 8.12155H12.8333ZM5.44444 8.12155C6.06576 8.12155 6.60913 8.62737 6.61111 9.28822V13.566C6.61111 14.1873 6.11159 14.7327 5.44444 14.7327H1.16667C0.545346 14.7327 0.00198049 14.2265 0 13.566V9.28822C0 8.6669 0.500973 8.12155 1.16667 8.12155H5.44444ZM12.8333 8.89933H8.55556C8.35612 8.89933 8.16667 9.06126 8.16667 9.28822V13.566C8.16667 13.7654 8.32329 13.9549 8.55556 13.9549H12.8333C13.0328 13.9549 13.2222 13.7983 13.2222 13.566V9.28822C13.2222 9.08879 13.0693 8.89933 12.8333 8.89933ZM5.44444 8.89933H1.16667C0.96723 8.89933 0.777778 9.06126 0.777778 9.28822V13.566C0.777778 13.7654 0.934404 13.9549 1.16667 13.9549H5.44444C5.64388 13.9549 5.83333 13.7983 5.83333 13.566V9.28822C5.83333 9.08879 5.68039 8.89933 5.44444 8.89933ZM12.8333 0.732666C13.4547 0.732666 13.998 1.23848 14 1.89933V6.17711C14 6.79843 13.5005 7.34378 12.8333 7.34378H8.55556C7.93424 7.34378 7.39087 6.83759 7.38889 6.17711V1.89933C7.38889 1.27801 7.88986 0.732666 8.55556 0.732666H12.8333ZM5.44444 0.732666C6.06576 0.732666 6.60913 1.23848 6.61111 1.89933V6.17711C6.61111 6.79843 6.11159 7.34378 5.44444 7.34378H1.16667C0.545346 7.34378 0.00198049 6.83759 0 6.17711V1.89933C0 1.27801 0.500973 0.732666 1.16667 0.732666H5.44444ZM12.8333 1.51044H8.55556C8.35612 1.51044 8.16667 1.67237 8.16667 1.89933V6.17711C8.16667 6.37655 8.32329 6.566 8.55556 6.566H12.8333C13.0328 6.566 13.2222 6.40938 13.2222 6.17711V1.89933C13.2222 1.6999 13.0693 1.51044 12.8333 1.51044ZM5.44444 1.51044H1.16667C0.96723 1.51044 0.777778 1.67237 0.777778 1.89933V6.17711C0.777778 6.37655 0.934404 6.566 1.16667 6.566H5.44444C5.64388 6.566 5.83333 6.40938 5.83333 6.17711V1.89933C5.83333 1.6999 5.68039 1.51044 5.44444 1.51044Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const List = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="16"
      height="12"
      viewBox="0 0 16 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M15.0363 5.53076H4.40907C4.07149 5.53076 3.79785 5.8044 3.79785 6.14198C3.79785 6.47956 4.07149 6.7532 4.40907 6.7532H15.0363C15.3738 6.7532 15.6475 6.47956 15.6475 6.14198C15.6475 5.8044 15.3738 5.53076 15.0363 5.53076Z"
        fill="currentColor"
      />
      <path
        d="M15.0363 0.946655H4.40907C4.07149 0.946655 3.79785 1.2203 3.79785 1.55787C3.79785 1.89545 4.07149 2.16909 4.40907 2.16909H15.0363C15.3738 2.16909 15.6475 1.89545 15.6475 1.55787C15.6475 1.2203 15.3738 0.946655 15.0363 0.946655Z"
        fill="currentColor"
      />
      <path
        d="M15.0363 10.1149H4.40907C4.07149 10.1149 3.79785 10.3885 3.79785 10.7261C3.79785 11.0637 4.07149 11.3373 4.40907 11.3373H15.0363C15.3738 11.3373 15.6475 11.0637 15.6475 10.7261C15.6475 10.3885 15.3738 10.1149 15.0363 10.1149Z"
        fill="currentColor"
      />
      <path
        d="M0.825144 2.38295C1.28086 2.38295 1.65029 2.01352 1.65029 1.55781C1.65029 1.1021 1.28086 0.732666 0.825144 0.732666C0.36943 0.732666 0 1.1021 0 1.55781C0 2.01352 0.36943 2.38295 0.825144 2.38295Z"
        fill="currentColor"
      />
      <path
        d="M0.825144 6.96706C1.28086 6.96706 1.65029 6.59763 1.65029 6.14192C1.65029 5.6862 1.28086 5.31677 0.825144 5.31677C0.36943 5.31677 0 5.6862 0 6.14192C0 6.59763 0.36943 6.96706 0.825144 6.96706Z"
        fill="currentColor"
      />
      <path
        d="M0.825144 11.5513C1.28086 11.5513 1.65029 11.1819 1.65029 10.7261C1.65029 10.2704 1.28086 9.901 0.825144 9.901C0.36943 9.901 0 10.2704 0 10.7261C0 11.1819 0.36943 11.5513 0.825144 11.5513Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const Minimize2 = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M14.2059 6.8843V1.84251H12.8765V9.15426H20.1882V7.82485H15.1464L21.6539 1.31407L20.7167 0.376831L14.2059 6.8843Z"
        fill="#1F4A70"
      />
      <path
        d="M7.55837 15.4125V20.4543H8.88778V13.1426H1.57603V14.472H6.61782L0.110352 20.9828L1.04759 21.92L7.55837 15.4125Z"
        fill="#1F4A70"
      />
    </svg>
  );
};
