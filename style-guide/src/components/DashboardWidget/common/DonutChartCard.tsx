import React, { useEffect, useRef, useState } from "react";
import Dynamic3DDonutChart from "./Dynamic3DDonutChart";
import "./styles/DonutChartCard.scss";
import { getPieChartRedirectionUrl } from "../util/util";
import useResizeObserver from "../hooks/useResizeObserver";
import { useMediaQuery } from "../hooks/useMediaQuery";

interface ChartItem {
  label: string;
  value: number;
  color: string;
}

interface DonutChartCardProps {
  title: string;
  data: ChartItem[];
  department?: string;
  configKey?: string;
  ga4EventTrigger?: any;
  activeTab?: string;
}

export const DonutChartCard: React.FC<DonutChartCardProps> = ({
  title,
  data,
  department,
  configKey,
  ga4EventTrigger,
  activeTab,
}) => {
  const formattedData = data.map((item) => ({
    label: item.label,
    value: item.value,
    color: item.color,
    url: getPieChartRedirectionUrl(item.label, activeTab, department),
  }));
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const containerWidth = useResizeObserver(chartContainerRef);
  const [renderChart, setRenderChart] = useState<boolean>(false);
  const isMediumScreen = useMediaQuery("md", "max");

  useEffect(() => {
    let timer;
    setRenderChart(false);
    timer = setTimeout(() => {
      setRenderChart(true);
    }, 500);
    return () => clearTimeout(timer);
  }, [containerWidth, activeTab]);

  return (
    <div className="donut-chart-card" ref={chartContainerRef}>
      <div className="donut-chart-header">
        <span className="donut-chart-title">{title}</span>
      </div>
      <div className="donut-chart-content-wrapper">
        <div className="donut-chart-visual">
          {renderChart ? (
            <Dynamic3DDonutChart
              data={formattedData}
              configKey={configKey}
              ga4EventTrigger={ga4EventTrigger}
              containerWidth={containerWidth}
              isMediumScreen={isMediumScreen}
            />
          ) : null}
        </div>
      </div>
    </div>
  );
};
