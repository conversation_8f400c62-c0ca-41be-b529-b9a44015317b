import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import { DonutChartAdapter } from "../../DashboardWidget/common/DonutChartAdapter";
import { VesselDisplayProps } from "../../DashboardWidget/types/card-types";

// Mock Dynamic3DDonutChart component
jest.mock("../../DashboardWidget/common/Dynamic3DDonutChart", () => {
  return function MockDynamic3DDonutChart(props: any) {
    return (
      <div data-testid="dynamic-3d-donut-chart">
        <div data-testid="chart-data-length">{props.data.length}</div>
        {props.data.map((item: any, index: number) => (
          <div key={index} data-testid={`chart-item-${index}`}>
            <span data-testid={`label-${index}`}>{item.label}</span>
            <span data-testid={`value-${index}`}>{item.value}</span>
            <span data-testid={`color-${index}`}>{item.color}</span>
            <span data-testid={`url-${index}`}>{item.url}</span>
          </div>
        ))}
      </div>
    );
  };
});

const mockVessels = [
  {
    name: "Vessel Alpha",
    vesselData: [10, 20, 30],
    type: "cargo",
    ra_level: "high",
  },
  {
    name: "Vessel Beta",
    vesselData: [5, 15],
    type: "tanker",
    ra_level: "medium",
  },
  {
    name: "Vessel Gamma",
    vesselData: [25, 35, 45, 55],
    type: "container",
    ra_level: "low",
  },
];

const mockProps: VesselDisplayProps = {
  vessels: mockVessels,
  tableHeaders: ["Name", "Type", "Status"],
  badgeColors: ["#ff0000", "#00ff00", "#0000ff", "#ffff00"],
  onSendEmail: jest.fn(),
  onVesselClick: jest.fn(),
  fetchNextPage: jest.fn(),
};

describe("DonutChartAdapter Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("renders without crashing", () => {
    render(<DonutChartAdapter {...mockProps} />);
    expect(screen.getByTestId("dynamic-3d-donut-chart")).toBeInTheDocument();
  });

  test("renders with correct container styling", () => {
    render(<DonutChartAdapter {...mockProps} />);

    const container = screen.getByTestId("dynamic-3d-donut-chart")
      .parentElement;
    expect(container).toHaveStyle({
      padding: "2rem",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      height: "100%",
    });
  });

  test("transforms vessel data correctly", () => {
    render(<DonutChartAdapter {...mockProps} />);

    // Check that all vessels are transformed
    expect(screen.getByTestId("chart-data-length")).toHaveTextContent("3");

    // Check first vessel transformation
    expect(screen.getByTestId("label-0")).toHaveTextContent("Vessel Alpha");
    expect(screen.getByTestId("value-0")).toHaveTextContent("60"); // 10 + 20 + 30
    expect(screen.getByTestId("color-0")).toHaveTextContent("#ff0000");
    expect(screen.getByTestId("url-0")).toHaveTextContent(
      "/vessel/vessel-alpha"
    );

    // Check second vessel transformation
    expect(screen.getByTestId("label-1")).toHaveTextContent("Vessel Beta");
    expect(screen.getByTestId("value-1")).toHaveTextContent("20"); // 5 + 15
    expect(screen.getByTestId("color-1")).toHaveTextContent("#00ff00");
    expect(screen.getByTestId("url-1")).toHaveTextContent(
      "/vessel/vessel-beta"
    );

    // Check third vessel transformation
    expect(screen.getByTestId("label-2")).toHaveTextContent("Vessel Gamma");
    expect(screen.getByTestId("value-2")).toHaveTextContent("160"); // 25 + 35 + 45 + 55
    expect(screen.getByTestId("color-2")).toHaveTextContent("#0000ff");
    expect(screen.getByTestId("url-2")).toHaveTextContent(
      "/vessel/vessel-gamma"
    );
  });

  test("handles empty vessels array", () => {
    const emptyProps = { ...mockProps, vessels: [] };
    render(<DonutChartAdapter {...emptyProps} />);

    expect(screen.getByTestId("chart-data-length")).toHaveTextContent("0");
  });

  test("handles null vessels", () => {
    const nullVesselsProps = { ...mockProps, vessels: null as any };
    render(<DonutChartAdapter {...nullVesselsProps} />);

    expect(screen.getByTestId("chart-data-length")).toHaveTextContent("0");
  });

  test("handles undefined vessels", () => {
    const undefinedVesselsProps = { ...mockProps, vessels: undefined as any };
    render(<DonutChartAdapter {...undefinedVesselsProps} />);

    expect(screen.getByTestId("chart-data-length")).toHaveTextContent("0");
  });

  test("cycles through badge colors correctly", () => {
    const manyVessels = Array.from({ length: 6 }, (_, i) => ({
      name: `Vessel ${i + 1}`,
      vesselData: [i + 1],
      type: "test",
      ra_level: "medium",
    }));

    const manyVesselsProps = { ...mockProps, vessels: manyVessels };
    render(<DonutChartAdapter {...manyVesselsProps} />);

    // Check color cycling (4 colors, 6 vessels)
    expect(screen.getByTestId("color-0")).toHaveTextContent("#ff0000"); // Index 0 % 4 = 0
    expect(screen.getByTestId("color-1")).toHaveTextContent("#00ff00"); // Index 1 % 4 = 1
    expect(screen.getByTestId("color-2")).toHaveTextContent("#0000ff"); // Index 2 % 4 = 2
    expect(screen.getByTestId("color-3")).toHaveTextContent("#ffff00"); // Index 3 % 4 = 3
    expect(screen.getByTestId("color-4")).toHaveTextContent("#ff0000"); // Index 4 % 4 = 0 (cycles back)
    expect(screen.getByTestId("color-5")).toHaveTextContent("#00ff00"); // Index 5 % 4 = 1
  });

  test("handles vessels with empty vesselData", () => {
    const emptyDataVessels = [
      {
        name: "Empty Vessel",
        vesselData: [],
        type: "test",
        ra_level: "low",
      },
    ];

    const emptyDataProps = { ...mockProps, vessels: emptyDataVessels };
    render(<DonutChartAdapter {...emptyDataProps} />);

    expect(screen.getByTestId("value-0")).toHaveTextContent("0");
  });

  test("handles vessels with single data point", () => {
    const singleDataVessels = [
      {
        name: "Single Data Vessel",
        vesselData: [42],
        type: "test",
        ra_level: "high",
      },
    ];

    const singleDataProps = { ...mockProps, vessels: singleDataVessels };
    render(<DonutChartAdapter {...singleDataProps} />);

    expect(screen.getByTestId("value-0")).toHaveTextContent("42");
  });

  test("generates correct URLs for vessel names with spaces and special characters", () => {
    const specialNameVessels = [
      {
        name: "Vessel With Spaces",
        vesselData: [10],
        type: "test",
        ra_level: "medium",
      },
      {
        name: "Vessel-With-Dashes",
        vesselData: [20],
        type: "test",
        ra_level: "high",
      },
      {
        name: "VESSEL IN CAPS",
        vesselData: [30],
        type: "test",
        ra_level: "low",
      },
    ];

    const specialNameProps = { ...mockProps, vessels: specialNameVessels };
    render(<DonutChartAdapter {...specialNameProps} />);

    expect(screen.getByTestId("url-0")).toHaveTextContent(
      "/vessel/vessel-with-spaces"
    );
    expect(screen.getByTestId("url-1")).toHaveTextContent(
      "/vessel/vessel-with-dashes"
    );
    expect(screen.getByTestId("url-2")).toHaveTextContent(
      "/vessel/vessel-in-caps"
    );
  });

  test("handles vessels with negative values in vesselData", () => {
    const negativeDataVessels = [
      {
        name: "Negative Vessel",
        vesselData: [-10, 20, -5],
        type: "test",
        ra_level: "medium",
      },
    ];

    const negativeDataProps = { ...mockProps, vessels: negativeDataVessels };
    render(<DonutChartAdapter {...negativeDataProps} />);

    expect(screen.getByTestId("value-0")).toHaveTextContent("5"); // -10 + 20 + (-5) = 5
  });

  test("handles vessels with zero values in vesselData", () => {
    const zeroDataVessels = [
      {
        name: "Zero Vessel",
        vesselData: [0, 0, 0],
        type: "test",
        ra_level: "low",
      },
    ];

    const zeroDataProps = { ...mockProps, vessels: zeroDataVessels };
    render(<DonutChartAdapter {...zeroDataProps} />);

    expect(screen.getByTestId("value-0")).toHaveTextContent("0");
  });

  test("handles large numbers in vesselData", () => {
    const largeDataVessels = [
      {
        name: "Large Data Vessel",
        vesselData: [1000000, 2000000, 3000000],
        type: "test",
        ra_level: "high",
      },
    ];

    const largeDataProps = { ...mockProps, vessels: largeDataVessels };
    render(<DonutChartAdapter {...largeDataProps} />);

    expect(screen.getByTestId("value-0")).toHaveTextContent("6000000");
  });

  test("memoizes chart data correctly", () => {
    const { rerender } = render(<DonutChartAdapter {...mockProps} />);

    // Get initial chart data
    const initialDataLength = screen.getByTestId("chart-data-length")
      .textContent;

    // Rerender with same props
    rerender(<DonutChartAdapter {...mockProps} />);

    // Should have same data
    expect(screen.getByTestId("chart-data-length")).toHaveTextContent(
      initialDataLength!
    );
  });

  test("updates chart data when vessels change", () => {
    const { rerender } = render(<DonutChartAdapter {...mockProps} />);

    expect(screen.getByTestId("chart-data-length")).toHaveTextContent("3");

    // Update with different vessels
    const newVessels = [mockVessels[0]]; // Only first vessel
    rerender(<DonutChartAdapter {...mockProps} vessels={newVessels} />);

    expect(screen.getByTestId("chart-data-length")).toHaveTextContent("1");
  });

  test("updates chart data when badgeColors change", () => {
    const { rerender } = render(<DonutChartAdapter {...mockProps} />);

    expect(screen.getByTestId("color-0")).toHaveTextContent("#ff0000");

    // Update with different colors
    const newColors = ["#purple", "#orange"];
    rerender(<DonutChartAdapter {...mockProps} badgeColors={newColors} />);

    expect(screen.getByTestId("color-0")).toHaveTextContent("#purple");
  });
});
