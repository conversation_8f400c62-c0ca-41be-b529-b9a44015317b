import React from "react";
import { CardDropdownSelectGroup } from "./CardSelectGroup";
import { MultiSelectConfig } from "../types/card-types";
import "./styles/CardContainer.scss";

interface CardDropdownSelectorsProps {
  multiSelects: MultiSelectConfig[];
  selectStates: string[][];
  onSelectChange: (selectIndex: number, newSelection: string[]) => void;
  ga4EventTrigger?: any;
  configKey?: string;
}

/**
 * This component is the CONTAINER for all the dropdowns.
 * It maps over the configurations and renders a VesselSelectGroup for each.
 */
export const CardDropdownSelectors: React.FC<CardDropdownSelectorsProps> = ({
  multiSelects,
  selectStates,
  onSelectChange,
  ga4EventTrigger,
  configKey,
}) => {
  if (!Array.isArray(multiSelects)) {
    return null;
  }

  return (
    <div className="ra-vessel-selects-container">
      {multiSelects.map((select, originalIndex) => {
        if (!select?.groups) {
          return null;
        }
        return (
          <CardDropdownSelectGroup
            // Use a unique ID from the data if available.
            // If not, a composite key or a unique string is better than just the index.
            // Assuming 'select.placeholder' is unique, or you have another unique property.
            key={select.placeholder || originalIndex}
            index={originalIndex}
            config={{
              placeholder: select.placeholder,
              width: select.width,
            }}
            selectedItems={selectStates[originalIndex] || []}
            groups={select.groups}
            onChange={onSelectChange}
            isSearchBoxVisible={select.isSearchBoxVisible}
            isSelectAllVisible={select.isSelectAllVisible}
            ga4EventTrigger={ga4EventTrigger}
            configKey={configKey}
          />
        );
      })}
    </div>
  );
};
