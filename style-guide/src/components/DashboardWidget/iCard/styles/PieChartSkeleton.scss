$overdue-color: #e61c6b;
$due-30-color: #f7a228;
$due-60-color: #298d41;
$label-color: #4a4a4a;
$text-color: #ffffff;
$background-color: #f5f5f5;
$border-color: #e0e0e0;
$loader-color: #e9e9e9;
$loader-shimmer: #f6f6f6;
$pie-loader-shimmer-color: #f6f6f6;

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0.5rem;
}

.top-loaders-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 1rem;
}

.top-loader-box {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 10px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

.loader-line-lg {
  height: 1.5rem;
  background: linear-gradient(90deg, #edf3f7 25%, #ffffff 50%, #edf3f7 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  width: 100%;
}

.loader-line-sm {
  height: 1.5rem;
  background: linear-gradient(90deg, #edf3f7 25%, #ffffff 50%, #edf3f7 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  width: 60%;
}

.pie-chart-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

@media (min-width: 1024px) and (max-width: 1500px) {
  .top-loaders-section,
  .pie-chart-cards-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }

  .pie-chart-cards-grid {
    height: 265px;
  }
}

.pie-chart-card {
  background-color: #ffffff;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.pie-chart-card-header {
  font-size: 0.8rem;
  font-weight: bold;
  padding: 1rem 1rem;
}

.pie-chart-card-content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.pie-chart-placeholder {
  width: 125px;
  height: 125px;
  background: linear-gradient(90deg, #edf3f7 25%, #ffffff 50%, #edf3f7 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 50%;
}

.pie-chart-legend {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.pie-chart-legend-item {
  display: flex;
  align-items: center;
  font-size: 0.6rem;
  color: #4a4a4a;
}

.color-box {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 0.7rem;
}

.overdue {
  background-color: #e61c6b;
}

.due-30 {
  background-color: #f7a228;
}

.others {
  background-color: #298d41;
}

.high {
  background-color: #e61c6b;
}

.medium {
  background-color: #f7a228;
}

.low {
  background-color: #298d41;
}