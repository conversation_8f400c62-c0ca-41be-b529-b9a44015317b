@use "./variables" as *;
@use "./mixins" as *;

.ra-vessel-grid-root {
  position: relative;
  height: 100%;
  @include media-min(lg) {
    overflow-y: hidden;
  }
}

.spinner-container {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.5);
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem 0;
}

.no-data-found {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.no-data-found-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 313px;
}

.ra-vessel-bar-chart-wrapper {
  width: 100%;
  overflow-x: auto;
}
