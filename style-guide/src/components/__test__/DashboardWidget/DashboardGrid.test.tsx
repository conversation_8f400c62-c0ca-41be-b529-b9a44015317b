import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import { DashboardGrid } from "../../DashboardWidget/common/DashboardGrid";
import { ChartData } from "../../DashboardWidget/types/card-types";

// Mock child components
jest.mock("../../DashboardWidget/common/DeficiencyCountCard", () => ({
  DeficiencyCountCard: (props: any) => (
    <div data-testid="deficiency-count-card">
      <div data-testid="card-title">{props.title}</div>
      <div data-testid="card-total">{props.total}</div>
    </div>
  ),
}));

jest.mock("../../DashboardWidget/common/DonutChartCard", () => ({
  DonutChartCard: (props: any) => (
    <div data-testid="donut-chart-card">
      <div data-testid="chart-title">{props.title}</div>
      <div data-testid="chart-size">{props.size || ""}</div>
      <div data-testid="chart-data-length">{props.data.length}</div>
      {props.data.map((item: any, index: number) => (
        <div key={index} data-testid={`chart-item-${index}`}>
          <span data-testid={`item-label-${index}`}>{item.label}</span>
          <span data-testid={`item-value-${index}`}>{item.value}</span>
          <span data-testid={`item-color-${index}`}>{item.color}</span>
        </div>
      ))}
    </div>
  ),
}));

// Mock SCSS import
jest.mock(
  "../../DashboardWidget/common/styles/DonutChartCard.scss",
  () => ({})
);

const mockChartData: ChartData = {
  openDeficiencies: {
    title: "Open Deficiencies",
    total: 45,
    data: [
      { label: "Overdue", value: 15, color: "#ff0000" },
      { label: "Due Soon", value: 20, color: "#ffff00" },
      { label: "Future", value: 10, color: "#00ff00" },
    ],
  },
  closedDeficiencies: {
    title: "Closed Deficiencies",
    total: 30,
    data: [
      { label: "High Severity", value: 8, color: "#ff0000" },
      { label: "Medium Severity", value: 12, color: "#ffff00" },
      { label: "Low Severity", value: 10, color: "#00ff00" },
    ],
  },
};

describe("DashboardGrid Component", () => {
  test("renders without crashing", () => {
    render(<DashboardGrid chartData={mockChartData} />);

    expect(
      document.querySelector(".dashboard-grid-container")
    ).toBeInTheDocument();
  });

  test("renders all four components", () => {
    render(<DashboardGrid chartData={mockChartData} />);

    const deficiencyCards = screen.getAllByTestId("deficiency-count-card");
    const donutCharts = screen.getAllByTestId("donut-chart-card");

    expect(deficiencyCards).toHaveLength(2);
    expect(donutCharts).toHaveLength(2);
  });

  test("renders DeficiencyCountCard components with correct props", () => {
    render(<DashboardGrid chartData={mockChartData} />);

    const cardTitles = screen.getAllByTestId("card-title");
    const cardTotals = screen.getAllByTestId("card-total");

    expect(cardTitles[0]).toHaveTextContent("Open Deficiencies");
    expect(cardTotals[0]).toHaveTextContent("45");

    expect(cardTitles[1]).toHaveTextContent("Closed Deficiencies");
    expect(cardTotals[1]).toHaveTextContent("30");
  });

  test("renders DonutChartCard components with correct props", () => {
    render(<DashboardGrid chartData={mockChartData} />);

    const chartTitles = screen.getAllByTestId("chart-title");
    const chartSizes = screen.getAllByTestId("chart-size");
    const chartDataLengths = screen.getAllByTestId("chart-data-length");

    // First donut chart (Due Date)
    expect(chartTitles[0]).toHaveTextContent("Due Date");
    expect(chartSizes[0]).toHaveTextContent("");
    expect(chartDataLengths[0]).toHaveTextContent("3");

    // Second donut chart (Severity)
    expect(chartTitles[1]).toHaveTextContent("Severity");
    expect(chartSizes[1]).toHaveTextContent("");
    expect(chartDataLengths[1]).toHaveTextContent("3");
  });

  test("passes correct data to second DonutChartCard (Severity)", () => {
    render(<DashboardGrid chartData={mockChartData} />);

    const donutCharts = screen.getAllByTestId("donut-chart-card");
    const secondChart = donutCharts[1];

    // Check second chart data (Severity uses closedDeficiencies.data)
    const itemLabels = secondChart.querySelectorAll(
      '[data-testid^="item-label-"]'
    );
    const itemValues = secondChart.querySelectorAll(
      '[data-testid^="item-value-"]'
    );
    const itemColors = secondChart.querySelectorAll(
      '[data-testid^="item-color-"]'
    );

    expect(itemLabels[0]).toHaveTextContent("High Severity");
    expect(itemValues[0]).toHaveTextContent("8");
    expect(itemColors[0]).toHaveTextContent("#ff0000");

    expect(itemLabels[1]).toHaveTextContent("Medium Severity");
    expect(itemValues[1]).toHaveTextContent("12");
    expect(itemColors[1]).toHaveTextContent("#ffff00");

    expect(itemLabels[2]).toHaveTextContent("Low Severity");
    expect(itemValues[2]).toHaveTextContent("10");
    expect(itemColors[2]).toHaveTextContent("#00ff00");
  });

  test("handles empty data arrays", () => {
    const emptyChartData: ChartData = {
      openDeficiencies: {
        title: "Open Deficiencies",
        total: 0,
        data: [],
      },
      closedDeficiencies: {
        title: "Closed Deficiencies",
        total: 0,
        data: [],
      },
    };

    render(<DashboardGrid chartData={emptyChartData} />);

    const chartDataLengths = screen.getAllByTestId("chart-data-length");
    expect(chartDataLengths[0]).toHaveTextContent("0");
    expect(chartDataLengths[1]).toHaveTextContent("0");

    const cardTotals = screen.getAllByTestId("card-total");
    expect(cardTotals[0]).toHaveTextContent("0");
    expect(cardTotals[1]).toHaveTextContent("0");
  });

  test("handles different title values", () => {
    const customTitleChartData: ChartData = {
      openDeficiencies: {
        title: "Custom Open Title",
        total: 100,
        data: mockChartData.openDeficiencies.data,
      },
      closedDeficiencies: {
        title: "Custom Closed Title",
        total: 50,
        data: mockChartData.closedDeficiencies.data,
      },
    };

    render(<DashboardGrid chartData={customTitleChartData} />);

    const cardTitles = screen.getAllByTestId("card-title");
    expect(cardTitles[0]).toHaveTextContent("Custom Open Title");
    expect(cardTitles[1]).toHaveTextContent("Custom Closed Title");
  });

  test("handles zero total values", () => {
    const zeroTotalChartData: ChartData = {
      openDeficiencies: {
        title: "Open Deficiencies",
        total: 0,
        data: mockChartData.openDeficiencies.data,
      },
      closedDeficiencies: {
        title: "Closed Deficiencies",
        total: 0,
        data: mockChartData.closedDeficiencies.data,
      },
    };

    render(<DashboardGrid chartData={zeroTotalChartData} />);

    const cardTotals = screen.getAllByTestId("card-total");
    expect(cardTotals[0]).toHaveTextContent("0");
    expect(cardTotals[1]).toHaveTextContent("0");
  });

  test("handles large total values", () => {
    const largeTotalChartData: ChartData = {
      openDeficiencies: {
        title: "Open Deficiencies",
        total: 999999,
        data: mockChartData.openDeficiencies.data,
      },
      closedDeficiencies: {
        title: "Closed Deficiencies",
        total: 888888,
        data: mockChartData.closedDeficiencies.data,
      },
    };

    render(<DashboardGrid chartData={largeTotalChartData} />);

    const cardTotals = screen.getAllByTestId("card-total");
    expect(cardTotals[0]).toHaveTextContent("999999");
    expect(cardTotals[1]).toHaveTextContent("888888");
  });

  test("maintains component structure", () => {
    render(<DashboardGrid chartData={mockChartData} />);

    const container = document.querySelector(".dashboard-grid-container");
    expect(container).toBeInTheDocument();

    // Check that all components are children of the container
    const deficiencyCards = container?.querySelectorAll(
      '[data-testid="deficiency-count-card"]'
    );
    const donutCharts = container?.querySelectorAll(
      '[data-testid="donut-chart-card"]'
    );

    expect(deficiencyCards).toHaveLength(2);
    expect(donutCharts).toHaveLength(2);
  });

  test("passes size prop correctly to DonutChartCard components", () => {
    render(<DashboardGrid chartData={mockChartData} />);

    const chartSizes = screen.getAllByTestId("chart-size");
    expect(chartSizes[0]).toHaveTextContent("");
    expect(chartSizes[1]).toHaveTextContent("");
  });

  test("creates correct data transformation for donut charts", () => {
    render(<DashboardGrid chartData={mockChartData} />);

    // Due Date chart should use openDeficiencies.data
    const chartTitles = screen.getAllByTestId("chart-title");
    expect(chartTitles[0]).toHaveTextContent("Due Date");

    // Severity chart should use closedDeficiencies.data
    expect(chartTitles[1]).toHaveTextContent("Severity");

    // Verify data is correctly passed
    const chartDataLengths = screen.getAllByTestId("chart-data-length");
    expect(chartDataLengths[0]).toHaveTextContent("3"); // openDeficiencies has 3 items
    expect(chartDataLengths[1]).toHaveTextContent("3"); // closedDeficiencies has 3 items
  });
});
