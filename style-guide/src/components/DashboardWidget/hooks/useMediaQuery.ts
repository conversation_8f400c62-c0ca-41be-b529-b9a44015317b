import { useState, useEffect } from 'react';

export const breakpoints = {
  xs: 0,
  sm: 600,
  md: 900,
  lg: 1200,
  xl: 1536,
} as const;

type Breakpoint = keyof typeof breakpoints | null;
type Direction = 'min' | 'max';

export function useMediaQuery(
  breakpoint: Breakpoint,
  direction: Direction = 'min',
  customBreakpoint: number | undefined = undefined,
): boolean {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    if (
      typeof window === 'undefined' ||
      typeof window.matchMedia !== 'function'
    ) {
      return;
    }

    const value = customBreakpoint ?? (breakpoint ? breakpoints?.[breakpoint] : 1536);
    const adjustedValue = direction === 'max' ? value - 0.02 : value;
    const query = `(${direction}-width: ${adjustedValue}px)`;

    const mediaQueryList = window.matchMedia(query);
    const updateMatch = () => setMatches(mediaQueryList.matches);

    updateMatch();

    if (mediaQueryList.addEventListener) {
      mediaQueryList.addEventListener('change', updateMatch);
      return () => mediaQueryList.removeEventListener('change', updateMatch);
    } else {
      // Deprecated fallback, cast to avoid TS warnings
      (mediaQueryList as any).addListener(updateMatch);
      return () => (mediaQueryList as any).removeListener(updateMatch);
    }
  }, [breakpoint, direction]);

  return matches;
}
