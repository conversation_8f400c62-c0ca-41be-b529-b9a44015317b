import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { <PERSON>rowserRouter } from "react-router-dom";
import "@testing-library/jest-dom";
import CardModule from "../../DashboardWidget/iCard/CardModule";

// Mock child components

jest.mock("../../DashboardWidget/iCard/CardGrid", () => {
  return function MockCardGrid(props: any) {
    return (
      <div data-testid="card-grid">
        Grid View - {props.vessels.length} vessels
      </div>
    );
  };
});

jest.mock("../../DashboardWidget/iCard/CardModuleHeader", () => ({
  CardModuleHeader: (props: any) => (
    <div data-testid="card-module-header">
      <button onClick={() => props.onViewModeChange("list")}>List</button>
      <button onClick={() => props.onViewModeChange("grid")}>Grid</button>
      <button onClick={props.onToggleModal}>Toggle Modal</button>
    </div>
  ),
}));

jest.mock("../../DashboardWidget/iCard/CardDropdownSelectors", () => ({
  CardDropdownSelectors: () => (
    <div data-testid="card-dropdown-selectors">Dropdowns</div>
  ),
}));

jest.mock("../../DashboardWidget/iCard/ModuleModal", () => ({
  ModuleModal: ({ children, isOpen }: any) =>
    isOpen ? <div data-testid="module-modal">{children}</div> : null,
}));

jest.mock("../../DashboardWidget/iCard/CardTabs", () => ({
  CardTabs: (props: any) => (
    <div data-testid="card-tabs">
      {props.tabs.map((tab: string) => (
        <button key={tab} onClick={() => props.onTabChange(tab)}>
          {tab}
        </button>
      ))}
    </div>
  ),
}));

// Mock CardList component
jest.mock("../../DashboardWidget/iCard/CardList", () => {
  return function MockCardList(props: any) {
    return (
      <div data-testid="card-list">
        List View - {props.vessels.length} vessels
      </div>
    );
  };
});

// Mock useMediaQuery hook
jest.mock("../../DashboardWidget/hooks/useMediaQuery", () => ({
  useMediaQuery: jest.fn(() => false),
}));

// Mock transform function for testing
const mockTransform = (vessels: any[]): any[] => {
  return vessels.map(v => ({
    ...v,
    name: `Transformed ${v.name}`
  }));
};

const mockProps = {
  title: "Test Dashboard",
  vessels: [
    { name: "Vessel A", status: "Active", vesselData: [5, 10] },
    { name: "Vessel B", status: "Inactive", vesselData: [8, 12] },
  ],
  staticData: {
    tabs: ["Active", "Inactive", "All"],
    tableHeaders: ["Vessel", "Status A", "Status B", "Action"],
    badgeColors: ["#ff0000", "#00ff00"],
    configKey: "test-config",
    severityData: { high: 5, medium: 3, low: 2 },
  },
  visibleConfig: {
    IsAllTabVisible: true,
    IsLastUpdatedVisible: true,
    IsRefereshIconVisible: true,
    IsDropdownVisible: true,
    IsTabsVisible: true,
    IsiconRenderVisible: true,
    IsenLargeIconVisible: true,
    filterApplyonRenderData: "all",
  },
  multiVesselSelects: [],
  componentView: {
    defaultComponent: "list" as const,
    gridComponent: "bar" as const,
  },
  sizeKey: "md" as const,
  onRefresh: jest.fn(),
  onSendEmail: jest.fn(),
  onVesselClick: jest.fn(),
  onChangeActiveTab: jest.fn(),
  fetchNextPage: jest.fn(),
  isFetchingNextPage: false,
  isLoading: false,
  pagination: { page: 1, totalPages: 1 },
  columns: [],
  responsive: false,
  responsiveConfig: {
    component: () => <div>Responsive Component</div>,
  },
};

const CardModuleWithRouter = (props: any) => (
  <BrowserRouter>
    <CardModule {...props} />
  </BrowserRouter>
);

describe("CardModule Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("renders without crashing", () => {
    render(<CardModuleWithRouter {...mockProps} />);
    expect(screen.getByTestId("card-module-header")).toBeInTheDocument();
  });

  test("renders in list view by default", () => {
    render(<CardModuleWithRouter {...mockProps} />);
    expect(screen.getByTestId("card-list")).toBeInTheDocument();
    expect(screen.queryByTestId("card-grid")).not.toBeInTheDocument();
  });

  test("switches to grid view when button is clicked", () => {
    render(<CardModuleWithRouter {...mockProps} />);

    fireEvent.click(screen.getByText("Grid"));

    expect(screen.getByTestId("card-grid")).toBeInTheDocument();
    expect(screen.queryByTestId("card-table")).not.toBeInTheDocument();
  });

  test("switches back to list view", () => {
    render(<CardModuleWithRouter {...mockProps} />);

    // Switch to grid first
    fireEvent.click(screen.getByText("Grid"));
    expect(screen.getByTestId("card-grid")).toBeInTheDocument();

    // Switch back to list
    fireEvent.click(screen.getByText("List"));
    expect(screen.getByTestId("card-list")).toBeInTheDocument();
    expect(screen.queryByTestId("card-grid")).not.toBeInTheDocument();
  });

  test("opens modal when toggle button is clicked", () => {
    render(<CardModuleWithRouter {...mockProps} />);

    fireEvent.click(screen.getByText("Toggle Modal"));

    expect(screen.getByTestId("module-modal")).toBeInTheDocument();
  });

  test("renders with grid view as default when specified", () => {
    const gridProps = {
      ...mockProps,
      componentView: {
        defaultComponent: "grid" as const,
        gridComponent: "bar" as const,
      },
    };

    render(<CardModuleWithRouter {...gridProps} />);
    expect(screen.getByTestId("card-grid")).toBeInTheDocument();
  });

  test("handles refresh callback", () => {
    render(<CardModuleWithRouter {...mockProps} />);

    // This would be triggered by the refresh icon in the header
    expect(mockProps.onRefresh).not.toHaveBeenCalled();
  });

  test("passes correct props to child components", () => {
    render(<CardModuleWithRouter {...mockProps} />);

    // Check that vessels are passed correctly
    expect(screen.getByText("List View - 2 vessels")).toBeInTheDocument();
  });

  // Additional comprehensive test cases
  test("renders with empty vessels array", () => {
    const emptyProps = { ...mockProps, vessels: [] };
    render(<CardModuleWithRouter {...emptyProps} />);

    expect(screen.getByTestId("card-module-header")).toBeInTheDocument();
    expect(screen.getByText("List View - 0 vessels")).toBeInTheDocument();
  });

  test("renders with different size keys", () => {
    const smallProps = { ...mockProps, sizeKey: "sm" as const };
    render(<CardModuleWithRouter {...smallProps} />);

    const container = document.querySelector(
      ".ra-vessel-card-container.size-sm"
    );
    expect(container).toBeInTheDocument();
  });

  test("renders with large size key", () => {
    const largeProps = { ...mockProps, sizeKey: "lg" as const };
    render(<CardModuleWithRouter {...largeProps} />);

    const container = document.querySelector(
      ".ra-vessel-card-container.size-lg"
    );
    expect(container).toBeInTheDocument();
  });

  test("handles loading state", () => {
    const loadingProps = { ...mockProps, isLoading: true };
    render(<CardModuleWithRouter {...loadingProps} />);

    expect(screen.getByTestId("card-list")).toBeInTheDocument();
  });

  test("handles fetching next page state", () => {
    const fetchingProps = { ...mockProps, isFetchingNextPage: true };
    render(<CardModuleWithRouter {...fetchingProps} />);

    expect(screen.getByTestId("card-list")).toBeInTheDocument();
  });

  test("renders last updated section when visible", () => {
    const visibleLastUpdatedProps = {
      ...mockProps,
      visibleConfig: {
        ...mockProps.visibleConfig,
        IsLastUpdatedVisible: true,
      },
    };

    render(<CardModuleWithRouter {...visibleLastUpdatedProps} />);
    expect(screen.getByText(/Last Updated on:/)).toBeInTheDocument();
  });

  test("hides last updated section when not visible", () => {
    const hiddenLastUpdatedProps = {
      ...mockProps,
      visibleConfig: {
        ...mockProps.visibleConfig,
        IsLastUpdatedVisible: false,
      },
    };

    render(<CardModuleWithRouter {...hiddenLastUpdatedProps} />);
    expect(screen.queryByText(/Last Updated on:/)).not.toBeInTheDocument();
  });

  test("renders refresh icon when visible", () => {
    const visibleRefreshProps = {
      ...mockProps,
      visibleConfig: {
        ...mockProps.visibleConfig,
        IsLastUpdatedVisible: true,
        IsRefereshIconVisible: true,
      },
    };

    render(<CardModuleWithRouter {...visibleRefreshProps} />);
    const refreshIcon = document.querySelector(".ra-refresh-icon");
    expect(refreshIcon).toBeInTheDocument();
  });

  test("calls onRefresh when refresh icon is clicked", () => {
    const onRefreshSpy = jest.fn();
    const refreshProps = {
      ...mockProps,
      onRefresh: onRefreshSpy,
      visibleConfig: {
        ...mockProps.visibleConfig,
        IsLastUpdatedVisible: true,
        IsRefereshIconVisible: true,
      },
    };

    render(<CardModuleWithRouter {...refreshProps} />);
    const refreshIcon = document.querySelector(".ra-refresh-icon");

    if (refreshIcon) {
      fireEvent.click(refreshIcon);
      expect(onRefreshSpy).toHaveBeenCalledTimes(1);
    }
  });

  test("closes modal when clicking outside", () => {
    render(<CardModuleWithRouter {...mockProps} />);

    // Open modal first
    fireEvent.click(screen.getByText("Toggle Modal"));
    expect(screen.getByTestId("module-modal")).toBeInTheDocument();

    // Close modal by clicking toggle again (use getAllByText to handle multiple instances)
    const toggleButtons = screen.getAllByText("Toggle Modal");
    fireEvent.click(toggleButtons[0]); // Click the first one (main component)
    expect(screen.queryByTestId("module-modal")).not.toBeInTheDocument();
  });

  test("renders with responsive configuration", () => {
    const responsiveProps = {
      ...mockProps,
      responsive: true,
      responsiveCardContainerHeight: "500px",
    };

    render(<CardModuleWithRouter {...responsiveProps} />);
    expect(screen.getByTestId("card-module-header")).toBeInTheDocument();
  });

  test("renders dropdown selectors when visible", () => {
    const dropdownProps = {
      ...mockProps,
      visibleConfig: {
        ...mockProps.visibleConfig,
        IsVesselSelectVisible: true,
        vesselSelectPosition: "before" as const,
      },
      multiVesselSelects: [{ id: 1, title: "Test Select", groups: [] }],
    };

    render(<CardModuleWithRouter {...dropdownProps} />);
    expect(screen.getByTestId("card-dropdown-selectors")).toBeInTheDocument();
  });

  test("renders tabs when visible", () => {
    const tabsProps = {
      ...mockProps,
      visibleConfig: {
        ...mockProps.visibleConfig,
        IsAlltabsVisible: true,
      },
    };

    render(<CardModuleWithRouter {...tabsProps} />);
    expect(screen.getByTestId("card-tabs")).toBeInTheDocument();
  });

  test("handles tab change callback", () => {
    const onChangeActiveTabSpy = jest.fn();
    const tabsProps = {
      ...mockProps,
      onChangeActiveTab: onChangeActiveTabSpy,
      visibleConfig: {
        ...mockProps.visibleConfig,
        IsAlltabsVisible: true,
      },
    };

    render(<CardModuleWithRouter {...tabsProps} />);

    // Click on a tab
    fireEvent.click(screen.getByText("Active"));
    expect(onChangeActiveTabSpy).toHaveBeenCalledWith("Active");
  });

  test("handles pagination correctly", () => {
    const paginationProps = {
      ...mockProps,
      pagination: { page: 2, totalPages: 5 },
    };

    render(<CardModuleWithRouter {...paginationProps} />);
    expect(screen.getByTestId("card-list")).toBeInTheDocument();
  });

  test("renders with multiVesselSelects configuration", () => {
    const multiSelectProps = {
      ...mockProps,
      multiVesselSelects: [
        { id: 1, title: "Select 1", options: [] },
        { id: 2, title: "Select 2", options: [] },
      ],
    };

    render(<CardModuleWithRouter {...multiSelectProps} />);
    expect(screen.getByTestId("card-module-header")).toBeInTheDocument();
  });

  test("handles vessel click callback", () => {
    const onVesselClickSpy = jest.fn();
    const vesselClickProps = {
      ...mockProps,
      onVesselClick: onVesselClickSpy,
    };

    render(<CardModuleWithRouter {...vesselClickProps} />);
    expect(screen.getByTestId("card-list")).toBeInTheDocument();
  });

  test("handles send email callback", () => {
    const onSendEmailSpy = jest.fn();
    const emailProps = {
      ...mockProps,
      onSendEmail: onSendEmailSpy,
    };

    render(<CardModuleWithRouter {...emailProps} />);
    expect(screen.getByTestId("card-list")).toBeInTheDocument();
  });

  test("renders modal with correct size class", () => {
    const modalProps = { ...mockProps, sizeKey: "lg" as const };
    render(<CardModuleWithRouter {...modalProps} />);

    // Open modal
    fireEvent.click(screen.getByText("Toggle Modal"));

    // Check if modal is rendered
    expect(screen.getByTestId("module-modal")).toBeInTheDocument();
  });

  describe("Data Filtering and Processing", () => {
    test('filters vessels by active tab when not "All"', () => {
      const filteredProps = {
        ...mockProps,
        vessels: [
          { name: "Vessel A", type: "Active", vesselData: [5, 10] },
          { name: "Vessel B", type: "Inactive", vesselData: [8, 12] },
          { name: "Vessel C", type: "Active", vesselData: [3, 7] },
        ],
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsAlltabsVisible: true,
        },
      };

      render(<CardModuleWithRouter {...filteredProps} />);

      // Click on Active tab
      fireEvent.click(screen.getByText("Active"));

      // Should filter to only Active vessels
      expect(screen.getByTestId("card-list")).toBeInTheDocument();
    });

    test('shows all vessels when "All" tab is selected', () => {
      const allTabProps = {
        ...mockProps,
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsAlltabsVisible: true,
          IsAllTabVisible: true,
        },
      };

      render(<CardModuleWithRouter {...allTabProps} />);

      // Should show all vessels by default (All tab)
      expect(screen.getByText("List View - 2 vessels")).toBeInTheDocument();
    });

    test("handles surveys-certificates config with vessel grouping", () => {
      const surveysProps = {
        ...mockProps,
        staticData: {
          ...mockProps.staticData,
          configKey: "surveys-certificates",
        },
        vessels: [
          {
            name: "Vessel A",
            vessel_id: 1,
            vesselData: [5, 10],
            countforBarChart: 15,
            overdue: 2,
            due_within_30_days: 3,
            due_within_60_days: 1,
          },
          {
            name: "Vessel A",
            vessel_id: 1,
            vesselData: [3, 7],
            countforBarChart: 10,
            overdue: 1,
            due_within_30_days: 2,
            due_within_60_days: 1,
          },
        ],
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsAlltabsVisible: true,
          IsAllTabVisible: true,
        },
      };

      render(<CardModuleWithRouter {...surveysProps} />);

      // Should group vessels with same vessel_id
      expect(screen.getByTestId("card-list")).toBeInTheDocument();
    });

    test("handles deficiencies config with vessel grouping", () => {
      const deficienciesProps = {
        ...mockProps,
        staticData: {
          ...mockProps.staticData,
          configKey: "deficiencies",
        },
        vessels: [
          {
            name: "Vessel A",
            vessel_id: 1,
            accepted_by_office: 5,
            not_accepted_by_office: 3,
            overdue: 2,
            due_within_30_days: 1,
            others: 2,
          },
          {
            name: "Vessel A",
            vessel_id: 1,
            accepted_by_office: 3,
            not_accepted_by_office: 2,
            overdue: 1,
            due_within_30_days: 2,
            others: 1,
          },
        ],
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsAlltabsVisible: true,
          IsAllTabVisible: true,
        },
      };

      render(<CardModuleWithRouter {...deficienciesProps} />);

      // Should group vessels with same vessel_id
      expect(screen.getByTestId("card-list")).toBeInTheDocument();
    });

    test("handles vessel filtering by vessel name selections", () => {
      const filterProps = {
        ...mockProps,
        multiVesselSelects: [
          {
            id: 1,
            title: "Vessel Select",
            groups: [
              {
                id: 1,
                title: "Group 1",
                vessels: [
                  { name: "Vessel A", vessel_id: 1 },
                  { name: "Vessel B", vessel_id: 2 },
                ],
              },
            ],
          },
        ],
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsVesselSelectVisible: true,
          vesselSelectPosition: "before" as const,
          filterApplyonRenderData: "vessel_id",
        },
      };

      render(<CardModuleWithRouter {...filterProps} />);
      expect(screen.getByTestId("card-list")).toBeInTheDocument();
    });

    test("applies vessel transformation when provided", () => {
      const transformProps = {
        ...mockProps,
        vessels: [{ name: "Test Vessel", type: "test", vesselData: [] }],
        staticData: {
          ...mockProps.staticData,
          transformVessels: mockTransform
        }
      };
      
      render(<CardModuleWithRouter {...transformProps} />);
      expect(screen.getByText("List View - 1 vessels")).toBeInTheDocument();

      // Since we're mocking CardList, we can't directly test for the transformed name
      // Instead, we can verify that transformVessels was called by checking the vessels length
      // and that the mock component rendered correctly
    });

    test("skips transformation when no transform function provided", () => {
      const noTransformProps = {
        ...mockProps,
        vessels: [{ name: "Test Vessel", type: "test", vesselData: [] }],
        staticData: {
          ...mockProps.staticData,
          transformVessels: undefined
        }
      };
      
      render(<CardModuleWithRouter {...noTransformProps} />);
      expect(screen.getByText("List View - 1 vessels")).toBeInTheDocument();
    });

    test("handles vessel filtering by vessel code", () => {
      const codeFilterProps = {
        ...mockProps,
        vessels: [
          {
            name: "Vessel A",
            vessel_code: "V001",
            vessel_account_code_new: "A001",
          },
          {
            name: "Vessel B",
            vessel_code: "V002",
            vessel_account_code_new: "A002",
          },
        ],
        visibleConfig: {
          ...mockProps.visibleConfig,
          filterApplyonRenderData: "vessel_code",
        },
      };

      render(<CardModuleWithRouter {...codeFilterProps} />);
      expect(screen.getByTestId("card-list")).toBeInTheDocument();
    });

    test("handles level RA filtering", () => {
      const raFilterProps = {
        ...mockProps,
        vessels: [
          { name: "Vessel A", ra_level: "High" },
          { name: "Vessel B", ra_level: "Medium" },
          { name: "Vessel C", ra_level: "Low" },
        ],
        multiVesselSelects: [
          { id: 1, groups: [] },
          { id: 2, groups: [] }, // Second select for RA level
        ],
      };

      render(<CardModuleWithRouter {...raFilterProps} />);
      expect(screen.getByTestId("card-list")).toBeInTheDocument();
    });
  });

  describe("Chart Data Processing", () => {
    test("processes deficiencies chart data correctly", () => {
      const deficienciesProps = {
        ...mockProps,
        staticData: {
          ...mockProps.staticData,
          configKey: "deficiencies",
          severityData: {
            All: { high: 10, medium: 5, low: 3 },
            Active: { high: 5, medium: 3, low: 1 },
          },
        },
        vessels: [
          {
            name: "Vessel A",
            overdue: 5,
            due_within_30_days: 3,
            others: 2,
            accepted_by_office: 8,
          },
          {
            name: "Vessel B",
            overdue: 2,
            due_within_30_days: 1,
            others: 1,
            accepted_by_office: 4,
          },
        ],
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsAlltabsVisible: true,
          IsAllTabVisible: true,
        },
      };

      render(<CardModuleWithRouter {...deficienciesProps} />);
      expect(screen.getByTestId("card-list")).toBeInTheDocument();
    });

    test("processes severity data for specific tab", () => {
      const specificTabProps = {
        ...mockProps,
        staticData: {
          ...mockProps.staticData,
          configKey: "deficiencies",
          severityData: {
            Active: { high: 5, medium: 3, low: 1 },
            Inactive: { high: 2, medium: 1, low: 0 },
          },
        },
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsAlltabsVisible: true,
          IsAllTabVisible: false,
        },
      };

      render(<CardModuleWithRouter {...specificTabProps} />);

      // Click on Active tab
      fireEvent.click(screen.getByText("Active"));
      expect(screen.getByTestId("card-list")).toBeInTheDocument();
    });

    test("handles missing severity data gracefully", () => {
      const noSeverityProps = {
        ...mockProps,
        staticData: {
          ...mockProps.staticData,
          configKey: "deficiencies",
          severityData: null,
        },
      };

      render(<CardModuleWithRouter {...noSeverityProps} />);
      expect(screen.getByTestId("card-list")).toBeInTheDocument();
    });
  });

  describe("Visibility Configuration", () => {
    test('shows dropdown selectors in "after" position', () => {
      const afterDropdownProps = {
        ...mockProps,
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsVesselSelectVisible: true,
          vesselSelectPosition: "after" as const,
        },
        multiVesselSelects: [{ id: 1, groups: [] }],
      };

      render(<CardModuleWithRouter {...afterDropdownProps} />);
      expect(screen.getByTestId("card-dropdown-selectors")).toBeInTheDocument();
    });

    test("hides dropdown selectors when not visible", () => {
      const hiddenDropdownProps = {
        ...mockProps,
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsVesselSelectVisible: false,
        },
      };

      render(<CardModuleWithRouter {...hiddenDropdownProps} />);
      expect(
        screen.queryByTestId("card-dropdown-selectors")
      ).not.toBeInTheDocument();
    });

    test("hides dropdown selectors in grid view", () => {
      const gridViewProps = {
        ...mockProps,
        componentView: {
          defaultComponent: "grid" as const,
          gridComponent: "bar" as const,
        },
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsVesselSelectVisible: true,
          vesselSelectPosition: "before" as const,
        },
      };

      render(<CardModuleWithRouter {...gridViewProps} />);
      expect(
        screen.queryByTestId("card-dropdown-selectors")
      ).not.toBeInTheDocument();
    });

    test("shows tabs when IsAlltabsVisible is true", () => {
      const visibleTabsProps = {
        ...mockProps,
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsAlltabsVisible: true,
        },
      };

      render(<CardModuleWithRouter {...visibleTabsProps} />);
      expect(screen.getByTestId("card-tabs")).toBeInTheDocument();
    });

    test("hides tabs when IsAlltabsVisible is false", () => {
      const hiddenTabsProps = {
        ...mockProps,
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsAlltabsVisible: false,
        },
      };

      render(<CardModuleWithRouter {...hiddenTabsProps} />);
      expect(screen.queryByTestId("card-tabs")).not.toBeInTheDocument();
    });

    test("handles icon visibility when no vessels data", () => {
      const noVesselsProps = {
        ...mockProps,
        vessels: [],
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsiconRenderVisible: true,
          IsenLargeIconVisible: true,
        },
      };

      render(<CardModuleWithRouter {...noVesselsProps} />);
      expect(screen.getByTestId("card-module-header")).toBeInTheDocument();
    });

    test("handles icon visibility when vessels data exists", () => {
      const withVesselsProps = {
        ...mockProps,
        vessels: [{ name: "Vessel A", vesselData: [5, 10] }],
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsiconRenderVisible: true,
          IsenLargeIconVisible: true,
        },
      };

      render(<CardModuleWithRouter {...withVesselsProps} />);
      expect(screen.getByTestId("card-module-header")).toBeInTheDocument();
    });
  });

  describe("Mobile and Responsive Behavior", () => {
    test("handles mobile/tablet responsive mode", () => {
      const {
        useMediaQuery,
      } = require("../../DashboardWidget/hooks/useMediaQuery");
      useMediaQuery.mockReturnValue(true); // Mock mobile/tablet

      const responsiveProps = {
        ...mockProps,
        responsive: true,
        responsiveCardContainerHeight: "600px",
      };

      render(<CardModuleWithRouter {...responsiveProps} />);
      expect(screen.getByTestId("card-list")).toBeInTheDocument();
    });

    test("handles desktop responsive mode", () => {
      const {
        useMediaQuery,
      } = require("../../DashboardWidget/hooks/useMediaQuery");
      useMediaQuery.mockReturnValue(false); // Mock desktop

      const responsiveProps = {
        ...mockProps,
        responsive: true,
      };

      render(<CardModuleWithRouter {...responsiveProps} />);
      expect(screen.getByTestId("card-list")).toBeInTheDocument();
    });
  });

  describe("State Management", () => {
    test('initializes with "All" tab when IsAllTabVisible is true', () => {
      const allTabVisibleProps = {
        ...mockProps,
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsAllTabVisible: true,
          IsAlltabsVisible: true,
        },
      };

      render(<CardModuleWithRouter {...allTabVisibleProps} />);
      expect(screen.getByTestId("card-tabs")).toBeInTheDocument();
    });

    test("initializes with first tab when IsAllTabVisible is false", () => {
      const firstTabProps = {
        ...mockProps,
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsAllTabVisible: false,
          IsAlltabsVisible: true,
        },
      };

      render(<CardModuleWithRouter {...firstTabProps} />);
      expect(screen.getByTestId("card-tabs")).toBeInTheDocument();
    });

    test("updates last updated timestamp on refresh", () => {
      const refreshProps = {
        ...mockProps,
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsLastUpdatedVisible: true,
          IsRefereshIconVisible: true,
        },
      };

      render(<CardModuleWithRouter {...refreshProps} />);

      const initialTime = screen.getByText(/Last Updated on:/).textContent;

      // Click refresh
      const refreshIcon = document.querySelector(".ra-refresh-icon");
      if (refreshIcon) {
        fireEvent.click(refreshIcon);

        // Time should be updated (though we can't easily test the exact time)
        expect(screen.getByText(/Last Updated on:/)).toBeInTheDocument();
      }
    });

    test("handles select state changes", () => {
      const selectProps = {
        ...mockProps,
        multiVesselSelects: [
          { id: 1, groups: [] },
          { id: 2, groups: [] },
        ],
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsVesselSelectVisible: true,
          vesselSelectPosition: "before" as const,
        },
      };

      render(<CardModuleWithRouter {...selectProps} />);
      expect(screen.getByTestId("card-dropdown-selectors")).toBeInTheDocument();
    });
  });

  describe("findMaxBarChartValue Function", () => {
    test("finds maximum value from countforBarChart", () => {
      const {
        findMaxBarChartValue,
      } = require("../../DashboardWidget/iCard/CardModule");

      const testData = [
        { countforBarChart: 10 },
        { countforBarChart: 25 },
        { countforBarChart: 15 },
      ];

      expect(findMaxBarChartValue(testData)).toBe(25);
    });

    test("returns 0 for empty array", () => {
      const {
        findMaxBarChartValue,
      } = require("../../DashboardWidget/iCard/CardModule");

      expect(findMaxBarChartValue([])).toBe(0);
    });

    test("returns 0 for null/undefined data", () => {
      const {
        findMaxBarChartValue,
      } = require("../../DashboardWidget/iCard/CardModule");

      expect(findMaxBarChartValue(null)).toBe(0);
      expect(findMaxBarChartValue(undefined)).toBe(0);
    });

    test("handles data with missing countforBarChart property", () => {
      const {
        findMaxBarChartValue,
      } = require("../../DashboardWidget/iCard/CardModule");

      const testData = [
        { name: "Item 1" },
        { countforBarChart: 10 },
        { name: "Item 3" },
      ];

      // Should handle undefined values gracefully
      expect(() => findMaxBarChartValue(testData)).not.toThrow();
    });
  });

  describe("Edge Cases and Error Handling", () => {
    test("handles null vessels gracefully", () => {
      const nullVesselsProps = { ...mockProps, vessels: null };

      expect(() => {
        render(<CardModuleWithRouter {...nullVesselsProps} />);
      }).not.toThrow();
    });

    test("handles undefined staticData properties", () => {
      const undefinedStaticProps = {
        ...mockProps,
        staticData: {
          tabs: [],
          tableHeaders: [],
          badgeColors: [],
          barChartMaxRange: 0,
        },
      };

      render(<CardModuleWithRouter {...undefinedStaticProps} />);
      expect(screen.getByTestId("card-module-header")).toBeInTheDocument();
    });

    test("handles missing visibleConfig properties", () => {
      const minimalVisibleProps = {
        ...mockProps,
        visibleConfig: {
          IsLastUpdatedVisible: false,
          IsRefereshIconVisible: false,
          filterApplyonRenderData: "vessel_id",
        },
      };

      render(<CardModuleWithRouter {...minimalVisibleProps} />);
      expect(screen.getByTestId("card-module-header")).toBeInTheDocument();
    });

    test("handles empty multiVesselSelects array", () => {
      const emptySelectsProps = {
        ...mockProps,
        multiVesselSelects: [],
      };

      render(<CardModuleWithRouter {...emptySelectsProps} />);
      expect(screen.getByTestId("card-module-header")).toBeInTheDocument();
    });

    test("handles missing componentView properties", () => {
      const minimalComponentViewProps = {
        ...mockProps,
        componentView: {},
      };

      render(<CardModuleWithRouter {...minimalComponentViewProps} />);
      expect(screen.getByTestId("card-list")).toBeInTheDocument(); // Should default to list
    });

    test("handles missing responsiveConfig", () => {
      const noResponsiveConfigProps = {
        ...mockProps,
        responsive: true,
        responsiveConfig: undefined,
      };

      expect(() => {
        render(<CardModuleWithRouter {...noResponsiveConfigProps} />);
      }).not.toThrow();
    });
  });

  describe("Performance and Optimization", () => {
    test("memoizes filtered vessels correctly", () => {
      const { rerender } = render(<CardModuleWithRouter {...mockProps} />);

      // Re-render with same props
      rerender(<CardModuleWithRouter {...mockProps} />);

      expect(screen.getByTestId("card-list")).toBeInTheDocument();
    });

    test("updates filtered vessels when dependencies change", () => {
      const { rerender } = render(<CardModuleWithRouter {...mockProps} />);

      const updatedProps = {
        ...mockProps,
        vessels: [
          { name: "New Vessel A", vesselData: [10, 20] },
          { name: "New Vessel B", vesselData: [15, 25] },
          { name: "New Vessel C", vesselData: [5, 15] },
        ],
      };

      rerender(<CardModuleWithRouter {...updatedProps} />);
      expect(screen.getByText("List View - 3 vessels")).toBeInTheDocument();
    });

    test("handles rapid tab changes", () => {
      const rapidTabProps = {
        ...mockProps,
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsAlltabsVisible: true,
        },
      };

      render(<CardModuleWithRouter {...rapidTabProps} />);

      // Rapidly change tabs
      fireEvent.click(screen.getByText("Active"));
      fireEvent.click(screen.getByText("Inactive"));
      fireEvent.click(screen.getByText("All"));

      expect(mockProps.onChangeActiveTab).toHaveBeenCalledTimes(3);
    });
  });

  describe("Integration with Child Components", () => {
    test("passes correct props to CardList in responsive mode", () => {
      const responsiveListProps = {
        ...mockProps,
        responsive: true,
        responsiveConfig: {
          component: () => <div>Responsive Component</div>,
        },
        responsiveCardListContainerHeight: "500px",
      };

      render(<CardModuleWithRouter {...responsiveListProps} />);
      expect(screen.getByTestId("card-list")).toBeInTheDocument();
    });

    test("passes correct props to CardGrid with chart data", () => {
      const gridProps = {
        ...mockProps,
        componentView: {
          defaultComponent: "grid" as const,
          gridComponent: "pie" as const,
        },
        staticData: {
          ...mockProps.staticData,
          configKey: "deficiencies",
        },
      };

      render(<CardModuleWithRouter {...gridProps} />);
      expect(screen.getByTestId("card-grid")).toBeInTheDocument();
    });

    test("passes ga4EventTrigger to child components", () => {
      const ga4Props = {
        ...mockProps,
        ga4EventTrigger: jest.fn(),
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsVesselSelectVisible: true,
          vesselSelectPosition: "before" as const,
        },
        multiVesselSelects: [{ id: 1, groups: [] }],
      };

      render(<CardModuleWithRouter {...ga4Props} />);
      expect(screen.getByTestId("card-dropdown-selectors")).toBeInTheDocument();
    });
  });

  describe("Scroll Reset Functionality", () => {
    test("resets scroll position when tab changes", async () => {
      // Create actual DOM elements for testing
      const mockTableContainer = document.createElement("div");
      mockTableContainer.className = "ra-tableContainer";
      mockTableContainer.scrollTop = 100;

      const mockChartContainer = document.createElement("div");
      mockChartContainer.className = "chart-scroll-container";
      mockChartContainer.scrollTop = 150;

      // Mock querySelectorAll before rendering
      const originalQuerySelectorAll = document.querySelectorAll;
      document.querySelectorAll = jest
        .fn()
        .mockReturnValueOnce([mockTableContainer]) // For .ra-tableContainer
        .mockReturnValueOnce([mockChartContainer]); // For .chart-scroll-container

      const scrollResetProps = {
        ...mockProps,
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsAlltabsVisible: true,
        },
      };

      render(<CardModuleWithRouter {...scrollResetProps} />);

      // Change tab to trigger scroll reset
      fireEvent.click(screen.getByText("Active"));

      // Wait for the timeout in useEffect
      await new Promise((resolve) => setTimeout(resolve, 100));

      expect(document.querySelectorAll).toHaveBeenCalledWith(
        ".ra-tableContainer"
      );
      expect(document.querySelectorAll).toHaveBeenCalledWith(
        ".chart-scroll-container"
      );
      expect(mockTableContainer.scrollTop).toBe(0);
      expect(mockChartContainer.scrollTop).toBe(0);

      // Restore original function
      document.querySelectorAll = originalQuerySelectorAll;
    });

    test("handles missing scroll containers gracefully", async () => {
      // Mock empty NodeList before rendering
      const originalQuerySelectorAll = document.querySelectorAll;
      document.querySelectorAll = jest.fn().mockReturnValue([]);

      const scrollResetProps = {
        ...mockProps,
        visibleConfig: {
          ...mockProps.visibleConfig,
          IsAlltabsVisible: true,
        },
      };

      render(<CardModuleWithRouter {...scrollResetProps} />);

      // Change tab - should not throw error
      expect(() => {
        fireEvent.click(screen.getByText("Inactive"));
      }).not.toThrow();

      await new Promise((resolve) => setTimeout(resolve, 100));

      // Restore original function
      document.querySelectorAll = originalQuerySelectorAll;
    });
  });
});
