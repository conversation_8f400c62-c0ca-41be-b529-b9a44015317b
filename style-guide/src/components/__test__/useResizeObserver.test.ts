import { renderHook } from "@testing-library/react";
import useResizeObserver from "../DashboardWidget/hooks/useResizeObserver";

describe("useResizeObserver", () => {
  const mockResizeObserver = jest.fn();
  let mockDisconnect: jest.Mock;
  let mockObserve: jest.Mock;

  beforeEach(() => {
    mockDisconnect = jest.fn();
    mockObserve = jest.fn();
    mockResizeObserver.mockImplementation(() => ({
      observe: mockObserve,
      disconnect: mockDisconnect,
    }));
    window.ResizeObserver = mockResizeObserver;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should not observe if ref.current is null", () => {
    const ref = { current: null };
    renderHook(() => useResizeObserver(ref));
    expect(mockObserve).not.toHaveBeenCalled();
  });
});
