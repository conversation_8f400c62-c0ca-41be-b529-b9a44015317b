import React from "react";
import BarChart from "../common/BarChart";
import { DashboardGrid } from "../common/DashboardGrid";
import { CardGridProps } from "../types/card-types";
import "./styles/CardGrid.scss";
import BarChartSkeletonLoader from "./BarChartSkeletonLoader";
import PieChartSkeletonLoader from "./PieChartSkeletonLoader";
import NoDataFound from "./NoDataFound";

// need to remove all inline css from it's all child component's hooks as well
export default function CardGrid({
  vessels,
  tableHeaders,
  badgeColors,
  chartData,
  barChartMaxRange = 250,
  isFetchingNextPage,
  isLoading,
  fetchNextPage,
  pagination,
  gridComponent = "bar",
  configKey,
  ga4EventTrigger,
  activeTab,
  department,
  isModal,
  isMobileOrTablet,
  ...rest
}: Readonly<CardGridProps>) {
  const renderContent = () => {
    if (gridComponent === "pie") {
      if (isLoading) {
        return <PieChartSkeletonLoader />;
      }

      if (
        !chartData ||
        (chartData.closedDeficiencies.total === 0 &&
          chartData.openDeficiencies.total === 0)
      ) {
        return (
          <NoDataFound />
        );
      } else {
        return (
          <DashboardGrid
            chartData={chartData}
            department={department}
            configKey={configKey}
            ga4EventTrigger={ga4EventTrigger}
            activeTab={activeTab}
          />
        );
      }
    }

    // Default to Bar Chart
    const safeVessels = Array.isArray(vessels) ? vessels : [];
    const barChartData = safeVessels.map((vessel) => {
      const values: Record<string, number | string> = {
        name: vessel.name,
        vessel_ownership_id: vessel.vessel_ownership_id || 0,
      };
      (tableHeaders || [])
        .filter((h) => h !== "Vessel" && h !== "Action")
        .forEach((header, idx) => {
          values[header] = (vessel.vesselData ?? [])[idx];
        });
      return values;
    });

    if (isLoading) {
      return <BarChartSkeletonLoader />;
    }

    if (safeVessels.length === 0) {
      return (
        <NoDataFound />
      );
    }

    // need to add a edge case for range of total vesselbar  count should not maximize the range limit here
    return (
      <div className="ra-vessel-bar-chart-wrapper">
        <BarChart
          vessels={barChartData}
          valueHeaders={tableHeaders.filter(
            (h) => h !== "Vessel" && h !== "Action"
          )}
          badgeColors={badgeColors}
          valueDomain={[0, barChartMaxRange]}
          configKey={configKey}
          ga4EventTrigger={ga4EventTrigger}
          activeTab={activeTab}
          isModal={isModal}
           isMobileOrTablet={isMobileOrTablet}
          {...rest}
        />
      </div>
    );
  };

  return <div className="ra-vessel-grid-root">{renderContent()}</div>;
}
