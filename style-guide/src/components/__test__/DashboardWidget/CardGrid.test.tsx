import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import CardGrid from "../../DashboardWidget/iCard/CardGrid";
import { Vessel, ChartData } from "../../DashboardWidget/types/card-types";

// Mock child components
jest.mock("../../DashboardWidget/common/BarChart", () => {
  return function MockBarChart(props: any) {
    return (
      <div data-testid="bar-chart">
        <div data-testid="chart-vessels">{JSON.stringify(props.vessels)}</div>
        <div data-testid="chart-value-domain">
          {props.valueDomain ? props.valueDomain.join(",") : ""}
        </div>
      </div>
    );
  };
});

jest.mock("../../DashboardWidget/common/DashboardGrid", () => ({
  DashboardGrid: function MockDashboardGrid(props: any) {
    return (
      <div data-testid="dashboard-grid">
        <div data-testid="has-chart-data">
          {props.chartData ? "true" : "false"}
        </div>
      </div>
    );
  },
}));

jest.mock("../../DashboardWidget/iCard/PieChartSkeletonLoader", () => {
  return function MockPieChartSkeletonLoader() {
    return <div data-testid="pie-chart-skeleton">Loading pie chart...</div>;
  };
});

jest.mock("../../DashboardWidget/iCard/BarChartSkeletonLoader", () => {
  return function MockBarChartSkeletonLoader() {
    return <div data-testid="bar-chart-skeleton">Loading bar chart...</div>;
  };
});

const mockVessels: Vessel[] = [
  {
    name: "Vessel 1",
    vesselData: ["data1", "data2"],
    type: "cargo",
    vessel_ownership_id: 1,
    risk_id: 1,
    vessel_id: 1,
    ra_level: "ROUTINE",
    vessel_code: "V001",
  },
  {
    name: "Vessel 2",
    vesselData: ["data3", "data4"],
    type: "tanker",
    vessel_ownership_id: 2,
    risk_id: 2,
    vessel_id: 2,
    ra_level: "SPECIAL",
    vessel_code: "V002",
  },
];

const mockChartData: ChartData = {
  openDeficiencies: {
    title: "Open Deficiencies",
    total: 10,
    data: [
      { label: "Critical", value: 3, color: "#ff0000" },
      { label: "Major", value: 4, color: "#ff8800" },
      { label: "Minor", value: 3, color: "#ffff00" },
    ],
  },
  closedDeficiencies: {
    title: "Closed Deficiencies",
    total: 5,
    data: [
      { label: "Critical", value: 1, color: "#ff0000" },
      { label: "Major", value: 2, color: "#ff8800" },
      { label: "Minor", value: 2, color: "#ffff00" },
    ],
  },
};

const mockPagination = {
  totalItems: 10,
  totalPages: 5,
  page: 1,
  pageSize: 2,
};

const defaultProps = {
  vessels: mockVessels,
  tableHeaders: ["Name", "Type", "Status"],
  badgeColors: ["#ff0000", "#00ff00", "#0000ff"],
  chartData: mockChartData,
  barChartMaxRange: 250,
  isFetchingNextPage: false,
  isLoading: false,
  fetchNextPage: jest.fn(),
  pagination: mockPagination,
  gridComponent: "bar" as const,
  onSendEmail: jest.fn(),
  onVesselClick: jest.fn(),
};

describe("CardGrid Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Rendering", () => {
    test("should render BarChart when gridComponent is bar (default)", () => {
      render(<CardGrid {...defaultProps} />);

      expect(screen.getByTestId("bar-chart")).toBeInTheDocument();
      // Bar chart receives computed value domain
      expect(screen.getByTestId("chart-value-domain")).toHaveTextContent(
        "0,250"
      );
    });

    test("should not render BarChart when gridComponent is pie", () => {
      const pieProps = { ...defaultProps, gridComponent: "pie" as const };
      render(<CardGrid {...pieProps} />);

      expect(screen.queryByTestId("bar-chart")).not.toBeInTheDocument();
    });

    test("should render BarChart when gridComponent is dashboard", () => {
      const dashboardProps = {
        ...defaultProps,
        gridComponent: "dashboard" as const,
      };
      render(<CardGrid {...dashboardProps} />);

      expect(screen.getByTestId("bar-chart")).toBeInTheDocument();
    });

    test("should render BarChart when gridComponent is bar", () => {
      render(<CardGrid {...defaultProps} />);

      expect(screen.getByTestId("bar-chart")).toBeInTheDocument();
      expect(screen.getByTestId("chart-value-domain")).toHaveTextContent(
        "0,250"
      );
    });

    test("should not render BarChart when gridComponent is pie", () => {
      const pieProps = { ...defaultProps, gridComponent: "pie" as const };
      render(<CardGrid {...pieProps} />);

      expect(screen.queryByTestId("bar-chart")).not.toBeInTheDocument();
    });

    test("should pass chart data to DashboardGrid in pie mode", () => {
      const pieProps = {
        ...defaultProps,
        gridComponent: "pie" as const,
        isLoading: false, // Changed to false so DashboardGrid renders instead of skeleton
      };
      render(<CardGrid {...pieProps} />);

      expect(screen.getByTestId("dashboard-grid")).toBeInTheDocument();
      expect(screen.getByTestId("has-chart-data")).toHaveTextContent("true");
    });
  });

  describe("Loading States", () => {
    test("should render DashboardGrid when pie and chartData provided", () => {
      const pieProps = {
        ...defaultProps,
        gridComponent: "pie" as const,
        isLoading: false, // Changed to false so DashboardGrid renders instead of skeleton
      };
      render(<CardGrid {...pieProps} />);

      expect(screen.getByTestId("dashboard-grid")).toBeInTheDocument();
      expect(screen.getByTestId("has-chart-data")).toHaveTextContent("true");
    });

    test("should render bar chart wrapper for non-pie components", () => {
      render(<CardGrid {...defaultProps} />);

      expect(screen.getByTestId("bar-chart")).toBeInTheDocument();
    });

    test("should render PieChartSkeletonLoader when pie component is loading", () => {
      const pieLoadingProps = {
        ...defaultProps,
        gridComponent: "pie" as const,
        isLoading: true,
      };
      render(<CardGrid {...pieLoadingProps} />);

      expect(screen.getByTestId("pie-chart-skeleton")).toBeInTheDocument();
      expect(screen.queryByTestId("dashboard-grid")).not.toBeInTheDocument();
    });

    test("should render BarChartSkeletonLoader when bar component is loading", () => {
      const barLoadingProps = {
        ...defaultProps,
        gridComponent: "bar" as const,
        isLoading: true,
      };
      render(<CardGrid {...barLoadingProps} />);

      expect(screen.getByTestId("bar-chart-skeleton")).toBeInTheDocument();
      expect(screen.queryByTestId("bar-chart")).not.toBeInTheDocument();
    });
  });

  describe("Chart Data", () => {
    test("should pass chart data to BarChart", () => {
      render(<CardGrid {...defaultProps} />);

      // We just assert that the BarChart is present for bar component
      expect(screen.getByTestId("bar-chart")).toBeInTheDocument();
    });

    test("should use custom barChartMaxRange", () => {
      const customRangeProps = { ...defaultProps, barChartMaxRange: 500 };
      render(<CardGrid {...customRangeProps} />);

      expect(screen.getByTestId("chart-value-domain")).toHaveTextContent(
        "0,500"
      );
    });

    test("should use default barChartMaxRange when not provided", () => {
      const { barChartMaxRange, ...propsWithoutRange } = defaultProps as any;
      render(<CardGrid {...propsWithoutRange} />);

      expect(screen.getByTestId("chart-value-domain")).toHaveTextContent(
        "0,250"
      );
    });

    test("should handle empty chart data", () => {
      const emptyChartData: ChartData = {
        openDeficiencies: { title: "Open", total: 0, data: [] },
        closedDeficiencies: { title: "Closed", total: 0, data: [] },
      };
      const emptyDataProps = { ...defaultProps, chartData: emptyChartData };

      render(<CardGrid {...emptyDataProps} />);

      expect(screen.getByTestId("bar-chart")).toBeInTheDocument();
    });
  });

  describe("BarChart Data Integration", () => {
    test("should pass vessels to BarChart (length)", () => {
      render(<CardGrid {...defaultProps} />);
      const json = screen.getByTestId("chart-vessels").textContent || "[]";
      const data = JSON.parse(json);
      expect(Array.isArray(data)).toBe(true);
      expect(data.length).toBe(mockVessels.length);
    });
  });

  describe("Edge Cases", () => {
    test("should handle null vessels", () => {
      const nullVesselsProps = { ...defaultProps, vessels: null as any };

      // Should not crash
      expect(() => render(<CardGrid {...nullVesselsProps} />)).not.toThrow();
    });

    test("should handle undefined chart data", () => {
      const undefinedChartProps = {
        ...defaultProps,
        chartData: undefined as any,
      };

      // Should not crash
      expect(() => render(<CardGrid {...undefinedChartProps} />)).not.toThrow();
    });

    test("should show no data message for pie chart with empty data", () => {
      const emptyPieChartData: ChartData = {
        openDeficiencies: { title: "Open", total: 0, data: [] },
        closedDeficiencies: { title: "Closed", total: 0, data: [] },
      };
      const emptyPieProps = {
        ...defaultProps,
        gridComponent: "pie" as const,
        chartData: emptyPieChartData,
        isLoading: false,
      };

      render(<CardGrid {...emptyPieProps} />);

      expect(screen.getByText("No Data Available")).toBeInTheDocument();
      expect(screen.queryByTestId("dashboard-grid")).not.toBeInTheDocument();
    });

    test("should show no data message for bar chart with empty vessels", () => {
      const emptyVesselsProps = {
        ...defaultProps,
        vessels: [],
        isLoading: false,
      };

      render(<CardGrid {...emptyVesselsProps} />);

      expect(screen.getByText("No Data Available")).toBeInTheDocument();
      expect(screen.queryByTestId("bar-chart")).not.toBeInTheDocument();
    });
  });

  describe("Component Integration", () => {
    test("should only show DashboardGrid for pie component when not loading", () => {
      const pieProps = {
        ...defaultProps,
        gridComponent: "pie" as const,
        isLoading: false, // Changed to false so DashboardGrid renders
      };
      render(<CardGrid {...pieProps} />);

      expect(screen.queryByTestId("bar-chart")).not.toBeInTheDocument();
      expect(screen.getByTestId("dashboard-grid")).toBeInTheDocument();
    });

    test("should show skeleton loader for pie component when loading", () => {
      const pieLoadingProps = {
        ...defaultProps,
        gridComponent: "pie" as const,
        isLoading: true,
      };
      render(<CardGrid {...pieLoadingProps} />);

      expect(screen.queryByTestId("bar-chart")).not.toBeInTheDocument();
      expect(screen.queryByTestId("dashboard-grid")).not.toBeInTheDocument();
      expect(screen.getByTestId("pie-chart-skeleton")).toBeInTheDocument();
    });
  });
});
