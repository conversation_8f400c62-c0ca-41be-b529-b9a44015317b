import React from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import "highcharts/highcharts-3d";
import { useHighchartsDonut } from "../hooks/useHighchartsDonut";
import "./styles/DonutChartCard.scss";
import { useMediaQuery } from "../hooks/useMediaQuery";

// Define the shape of the data items for the chart
interface ChartDataItem {
  label: string;
  value: number;
  color: string;
  url: string;
}

interface DonutChartProps {
  data: ChartDataItem[];
  configKey?: string;
  ga4EventTrigger?: any;
  containerWidth?: number;
  isMediumScreen: boolean;
}

const Dynamic3DDonutChart: React.FC<DonutChartProps> = ({
  data,
  configKey,
  ga4EventTrigger,
  containerWidth = 300,
  isMediumScreen,
}) => {
  const isExtraLargeScreen = useMediaQuery("xl", "min");
  const isLargeScreen = useMediaQuery(null, "min", 1340);
  let pieChartSize = "30%";
  if (isMediumScreen) {
    pieChartSize = "100%";
  }
  if (isLargeScreen) {
    pieChartSize = "60%";
  }
  if (isExtraLargeScreen) {
    pieChartSize = "80%";
  }
  const { options, tooltipStyle } = useHighchartsDonut({
    data,
    size: pieChartSize,
    configKey,
    ga4EventTrigger,
    showLabels: !isMediumScreen,
  });
  return (
    <div
      className="donut-chart-container"
      style={{ width: `${containerWidth - 10}px` }}
    >
      <style>{tooltipStyle}</style>
      <HighchartsReact
        highcharts={Highcharts}
        options={options}
        containerProps={{ className: "donut-chart-highchart" }}
      />
      <div className="legend-container">
        {data.map((item) => (
          <div
            className="legend-item"
            key={item.label}
          >
            <span className="legend-item-circle" style={{ background: item?.color || '#fff' }}/>
            {item?.label ?? ''}
          </div>
        ))}
      </div>
    </div>
  );
};

export default Dynamic3DDonutChart;
