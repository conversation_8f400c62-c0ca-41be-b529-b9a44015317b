import {
  getSurveyActiveDueTab,
  getGACategory,
  getPieChartRedirectionUrl,
} from "./../../DashboardWidget/util/util";
import {
  WidgetConstant,
  GA_Category,
} from "./../../DashboardWidget/types/widget.constant";

describe("getSurveyActiveDueTab", () => {
  it("should return 'overdue' when value is 'Overdue'", () => {
    expect(getSurveyActiveDueTab("Overdue")).toBe("overdue");
  });

  it("should return 'due30' when value is 'Due within 30 Days'", () => {
    expect(getSurveyActiveDueTab("Due within 30 Days")).toBe("due30");
  });

  it("should return 'due60' when value is 'Due within 60 Days'", () => {
    expect(getSurveyActiveDueTab("Due within 60 Days")).toBe("due60");
  });

  it("should return an empty string for unknown values", () => {
    expect(getSurveyActiveDueTab("Invalid Value")).toBe("");
  });

  it("should return an empty string for null", () => {
    expect(getSurveyActiveDueTab(null)).toBe("");
  });

  it("should return an empty string for undefined", () => {
    expect(getSurveyActiveDueTab(undefined)).toBe("");
  });

  it("should return an empty string for empty string input", () => {
    expect(getSurveyActiveDueTab("")).toBe("");
  });

  it("should handle case-sensitive matching", () => {
    expect(getSurveyActiveDueTab("overdue")).toBe("");
    expect(getSurveyActiveDueTab("OVERDUE")).toBe("");
    expect(getSurveyActiveDueTab("due within 30 days")).toBe("");
  });

  it("should handle partial matches", () => {
    expect(getSurveyActiveDueTab("Due within")).toBe("");
    expect(getSurveyActiveDueTab("30 Days")).toBe("");
    expect(getSurveyActiveDueTab("Overdue items")).toBe("");
  });

  it("should handle whitespace variations", () => {
    expect(getSurveyActiveDueTab(" Overdue ")).toBe("");
    expect(getSurveyActiveDueTab("Due  within  30  Days")).toBe("");
  });
});

describe("getGACategory", () => {
  it("should return correct GA category for OWNER_FINANCIAL_REPORTING", () => {
    expect(getGACategory(WidgetConstant.OWNER_FINANCIAL_REPORTING)).toBe(
      GA_Category.OWNER_FINANCIAL_REPORTING
    );
  });

  it("should return correct GA category for ITINERARY_ETA", () => {
    expect(getGACategory(WidgetConstant.ITINERARY_ETA)).toBe(
      GA_Category.ITINERARY_ETA
    );
  });

  it("should return correct GA category for RISK_ASSESSMENT", () => {
    expect(getGACategory(WidgetConstant.RISK_ASSESSMENT)).toBe(
      GA_Category.RISK_ASSESSMENT
    );
  });

  it("should return correct GA category for DEFICIENCIES", () => {
    expect(getGACategory(WidgetConstant.DEFICIENCIES)).toBe(
      GA_Category.DEFICIENCIES
    );
  });

  it("should return correct GA category for SURVEYS_CERTIFICATES", () => {
    expect(getGACategory(WidgetConstant.SURVEYS_CERTIFICATES)).toBe(
      GA_Category.SURVEYS_CERTIFICATES
    );
  });

  it("should return undefined for unknown configKey", () => {
    expect(getGACategory("unknown-config")).toBeUndefined();
  });

  it("should return undefined for null configKey", () => {
    expect(getGACategory(null)).toBeUndefined();
  });

  it("should return undefined for undefined configKey", () => {
    expect(getGACategory(undefined)).toBeUndefined();
  });

  it("should return undefined for empty string configKey", () => {
    expect(getGACategory("")).toBeUndefined();
  });

  it("should handle case-sensitive matching", () => {
    expect(getGACategory("OWNER-FINANCIAL-REPORTING")).toBeUndefined();
    expect(getGACategory("Owner-Financial-Reporting")).toBeUndefined();
  });

  it("should handle all widget constants correctly", () => {
    const widgetConstants = Object.values(WidgetConstant);
    const gaCategories = Object.values(GA_Category);

    widgetConstants.forEach((constant) => {
      const result = getGACategory(constant);
      expect(gaCategories).toContain(result);
    });
  });

  it("should maintain consistency between widget constants and GA categories", () => {
    expect(
      getGACategory(WidgetConstant.OWNER_FINANCIAL_REPORTING)
    ).toBeDefined();
    expect(getGACategory(WidgetConstant.ITINERARY_ETA)).toBeDefined();
    expect(getGACategory(WidgetConstant.RISK_ASSESSMENT)).toBeDefined();
    expect(getGACategory(WidgetConstant.DEFICIENCIES)).toBeDefined();
    expect(getGACategory(WidgetConstant.SURVEYS_CERTIFICATES)).toBeDefined();
  });

  it("should return different values for different widget constants", () => {
    const results = Object.values(WidgetConstant).map(getGACategory);
    const uniqueResults = new Set(results);
    expect(uniqueResults.size).toBe(results.length);
  });
});

describe("getPieChartRedirectionUrl", () => {
  it("should return empty string when department is undefined", () => {
    expect(getPieChartRedirectionUrl("Overdue", "Defect", undefined)).toBe("");
  });

  it("should return empty string when activeTab is undefined", () => {
    expect(getPieChartRedirectionUrl("Overdue", undefined, "TechDept")).toBe(
      ""
    );
  });

  it("should add overdue param when label is 'Overdue'", () => {
    const url = getPieChartRedirectionUrl("Overdue", "Defect", "TechDept");
    expect(url).toContain("overdue=true");
    expect(url).toContain("techGroup=TechDept");
    expect(url).toContain("defectsOnly=true");
  });

  it("should add due=30d param when label is 'Due within 30 Days'", () => {
    const url = getPieChartRedirectionUrl(
      "Due within 30 Days",
      "Defect",
      "TechDept"
    );
    expect(url).toContain("due=30d");
  });

  it("should add due=60d param when label is 'Others'", () => {
    const url = getPieChartRedirectionUrl("Others", "Defect", "TechDept");
    expect(url).toContain("due=60d");
  });

  it("should add severity=high param when label is 'High'", () => {
    const url = getPieChartRedirectionUrl("High", "Defect", "TechDept");
    expect(url).toContain("severity=high");
  });

  it("should add severity=medium param when label is 'Medium'", () => {
    const url = getPieChartRedirectionUrl("Medium", "Defect", "TechDept");
    expect(url).toContain("severity=medium");
  });

  it("should add severity=low param when label is 'Low'", () => {
    const url = getPieChartRedirectionUrl("Low", "Defect", "TechDept");
    expect(url).toContain("severity=low");
  });

  it("should add defectsOnly=true when activeTab is 'Defect'", () => {
    const url = getPieChartRedirectionUrl("Overdue", "Defect", "TechDept");
    expect(url).toContain("defectsOnly=true");
  });

  it("should add typeOfInspection param when activeTab is 'Technical Follow-up'", () => {
    const url = getPieChartRedirectionUrl(
      "Overdue",
      "Technical Follow-up",
      "TechDept"
    );
    expect(url).toContain("typeOfInspection=Technical Follow Up");
  });

  it("should not add extra params for unknown label", () => {
    const url = getPieChartRedirectionUrl("UnknownLabel", "Defect", "TechDept");
    expect(url).toBe(
      "deficiency/list?acceptedByOffice=false&techGroup=TechDept&defectsOnly=true"
    );
  });

  it("should not add extra params for unknown activeTab", () => {
    const url = getPieChartRedirectionUrl("Overdue", "UnknownTab", "TechDept");
    expect(url).toBe(
      "deficiency/list?acceptedByOffice=false&techGroup=TechDept&overdue=true"
    );
  });
});
