import { useEffect, useState, RefObject } from 'react';

const useResizeObserver = (ref: RefObject<HTMLElement>): number => {
  const [width, setWidth] = useState(0);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const resizeObserver = new ResizeObserver(([entry]) => {
      const newWidth = entry.contentRect.width;
      setWidth((prevWidth) => (prevWidth !== newWidth ? newWidth : prevWidth));
    });

    resizeObserver.observe(element);

    return () => resizeObserver.disconnect();
  }, [ref]);

  return width;
};

export default useResizeObserver;
