import React, { useMemo, useState, useCallback, useEffect } from "react";
import { RotateCw } from "lucide-react";
import classNames from "classnames";
import {
  Vessel,
  MultiSelectConfig,
  GridComponentType,
} from "../types/card-types";
import CardList from "./CardList";
import CardGrid from "./CardGrid";
import { CardModuleHeader } from "./CardModuleHeader";
import { CardDropdownSelectors } from "./CardDropdownSelectors";
import { ModuleModal } from "./ModuleModal";
import { CardTabs } from "./CardTabs";
import "./styles/CardContainer.scss";
import { ColumnDef } from "@tanstack/react-table";
import { useMediaQuery } from "../hooks/useMediaQuery";
import { formatUTCDate } from "../util/util";
import { WidgetConstant } from "../types/widget.constant";
export interface CardModuleProps {
  readonly title: string;
  readonly vessels: Vessel[];
  readonly multiVesselSelects: MultiSelectConfig[];

  readonly staticData: {
    readonly tabs: string[];
    readonly tableHeaders: string[];
    readonly badgeColors: string[];
    readonly tableHeaders1?: string[];
    readonly tableHeaders2?: string[];
    readonly severityData?: any; //it may change
    readonly barChartMaxRange: number;
    readonly configKey?: string;
    readonly transformVessels?: (vessels: Vessel[]) => Vessel[];
  };

  // Callbacks
  readonly onRefresh: () => void;
  readonly onSendEmail: (vessel: Vessel) => void;
  readonly onVesselClick: (vessel: Vessel) => void;
  readonly fetchNextPage: () => void;
  readonly onChangeActiveTab: (activeTab: string) => void;

  readonly sizeKey: "sm" | "md" | "lg";

  // State flags
  readonly visibleConfig: {
    readonly IsiconRenderVisible?: boolean;
    readonly IsenLargeIconVisible?: boolean;
    readonly IsVesselSelectVisible?: boolean;
    readonly IsAlltabsVisible?: boolean;
    readonly IsAllTabVisible?: boolean;
    readonly IsLastUpdatedVisible: boolean;
    readonly IsRefereshIconVisible: boolean;
    readonly vesselSelectPosition?: "before" | "after";
    readonly filterApplyonRenderData: string;
  };
  columns: ColumnDef<Vessel>[];
  readonly isFetchingNextPage?: boolean;
  readonly isLoading?: boolean;
  readonly pagination: any; // Using `any` for simplification as the exact type is complex

  readonly componentView: {
    readonly gridComponent?: GridComponentType;
    readonly defaultComponent?: "list" | "grid";
  };
  readonly responsive: boolean;
  readonly responsiveConfig: {
    component: React.ComponentType<{ data: Vessel }>;
  };
  responsiveCardContainerHeight?: string;
  responsiveCardListContainerHeight?: string;
  ga4EventTrigger?: any;
  department?: string;
}

export const findMaxBarChartValue = (data: any[]): number => {
  if (!data || data.length === 0) {
    return 0;
  }

  const values = data.map((item) => item.countforBarChart);
  return Math.max(...values);
};

export default function CardModule({
  title,
  vessels,
  staticData,
  visibleConfig,
  multiVesselSelects = [],
  componentView,
  sizeKey = "md",
  onRefresh,
  onSendEmail,
  onVesselClick,
  fetchNextPage,
  onChangeActiveTab,
  isFetchingNextPage,
  isLoading,
  pagination,
  columns: columnsProp,
  responsive,
  responsiveConfig,
  responsiveCardContainerHeight,
  responsiveCardListContainerHeight,
  ga4EventTrigger,
  department,
}: Readonly<CardModuleProps>) {
  // Destructure configKey, tableHeaders1, tableHeaders2, and badgeColors
  const {
    configKey,
    severityData,
    tableHeaders1,
    tableHeaders2,
    badgeColors,
  } = staticData;
  const isMobileOrTablet = useMediaQuery("lg", "max");
  const [viewMode, setViewMode] = useState<"list" | "grid">(
    componentView?.defaultComponent || "list"
  );
  const [activeTab, setActiveTab] = useState<string>(() => {
    if (visibleConfig?.IsAllTabVisible) {
      return "All"; // default to All tab when visible
    }
    return staticData.tabs[0] || ""; // default to first real tab
  });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [selectStates, setSelectStates] = useState<string[][]>(() =>
    multiVesselSelects.map(() => [])
  );

  const filterKeyMapping = {
    vessel_id: { dropdownKey: "vessel_id", renderKey: "vessel_id" },
    vessel_code: {
      dropdownKey: "vessel_account_code_new",
      renderKey: "vessel_code",
    },
  };

  const handleTabChange = useCallback(
    (tab: string) => {
      setActiveTab(tab); // Update the local state
      onChangeActiveTab(tab); // Call the parent's callback
    },
    [onChangeActiveTab]
  );

  // Reset scroll position when activeTab changes
  useEffect(() => {
    const resetScrollPosition = () => {
      // Reset scroll for table containers (CardList and ListWrapper)
      const tableContainers = document.querySelectorAll(".ra-tableContainer");
      tableContainers.forEach((container) => {
        if (container instanceof HTMLElement) {
          container.scrollTop = 0;
        }
      });

      // Reset scroll for chart containers (CardGrid BarChart)
      const chartContainers = document.querySelectorAll(
        ".chart-scroll-container"
      );
      chartContainers.forEach((container) => {
        if (container instanceof HTMLElement) {
          container.scrollTop = 0;
        }
      });
    };

    // Small delay to ensure DOM is updated after tab change
    const timeoutId = setTimeout(resetScrollPosition, 50);

    return () => clearTimeout(timeoutId);
  }, [activeTab]);

  const { filteredVessels, chartData } = useMemo(() => {
    let defaultChartData: any;
    if (configKey === WidgetConstant.DEFICIENCIES)
      defaultChartData = {
        openDeficiencies: {
          title: "Open Deficiencies",
          total: 0,
          data: [
            {
              label: tableHeaders1?.[0] || "Overdue",
              value: 0,
              color: badgeColors?.[0] || "#d80e61",
            },
            {
              label: tableHeaders1?.[1] || "Due within 30 days",
              value: 0,
              color: badgeColors?.[1] || "#fbc02d",
            },
            {
              label: tableHeaders1?.[2] || "Others",
              value: 0,
              color: badgeColors?.[2] || "#27a527",
            },
          ],
        },
        closedDeficiencies: {
          title: "Closed Deficiencies",
          total: 0,
          data: [
            {
              label: tableHeaders2?.[0] || "High",
              value: 0,
              color: badgeColors?.[0] || "#d80e61",
            },
            {
              label: tableHeaders2?.[1] || "Medium",
              value: 0,
              color: badgeColors?.[1] || "#fbc02d",
            },
            {
              label: tableHeaders2?.[2] || "Low",
              value: 0,
              color: badgeColors?.[2] || "#27a527",
            },
          ],
        },
      };
    if (!vessels) {
      return { filteredVessels: [], chartData: defaultChartData };
    }

    let filteredData = [...vessels];

    // Filter by Active Tab
    if (activeTab !== "All") {
      filteredData = filteredData.filter((v: Vessel) => v.type === activeTab);
    } else if (staticData.transformVessels) {
      // Apply vessel transformation if provided
      filteredData = staticData.transformVessels(filteredData);
    }

    // Filter 1: Vessel Name
    const vesselNameSelections = selectStates[0];
    if (vesselNameSelections?.length > 0) {
      const allVesselOptions =
        multiVesselSelects[0]?.groups.flatMap((g) => g.vessels) || [];

      // Use the mapping to get the correct keys
      const keys =
        filterKeyMapping[visibleConfig.filterApplyonRenderData] ||
        filterKeyMapping.vessel_id;

      //selectedVesselIdentifiers set of  vessel_id's of all selecte active vessels names from the first select box
      const selectedVesselIdentifiers = new Set(
        allVesselOptions
          .filter((opt) => vesselNameSelections.includes(opt.name))
          .map((opt) => `${opt[keys.dropdownKey]}`)
      );

      //the filteredData array contains objects with vessel.id, so filter out them to render on table or grid view
      if (selectedVesselIdentifiers.size > 0) {
        filteredData = filteredData.filter((vessel) =>
          selectedVesselIdentifiers.has(`${vessel[keys.renderKey]}`)
        );
      }
    }

    // Filter 2: Level RA
    const levelRaSelections = selectStates[1];
    if (levelRaSelections?.length > 0) {
      filteredData = filteredData.filter((vessel: Vessel) => {
        return levelRaSelections.includes(vessel.ra_level);
      });
    }

    let newChartData = defaultChartData;
    if (configKey === WidgetConstant.DEFICIENCIES) {
      const totalOverdue = filteredData.reduce(
        (sum: number, item: any) => sum + (item.overdue || 0),
        0
      );
      const totalDueWithin30Days = filteredData.reduce(
        (sum: number, item: any) => sum + (item.due_within_30_days || 0),
        0
      );
      const totalOthers = filteredData.reduce(
        (sum: number, item: any) => sum + (item.others || 0),
        0
      );
      const totalOpenDeficiencies =
        totalOverdue + totalDueWithin30Days + totalOthers;

      const closedDeficiencyTotals = {
        high: 0,
        medium: 0,
        low: 0,
      };

      if (severityData) {
        if (activeTab === "All") {
          // For "All" tab, sum up all severity values
          for (const category in severityData) {
            if (severityData.hasOwnProperty(category)) {
              const severity = severityData[category];
              closedDeficiencyTotals.high += severity.high || 0;
              closedDeficiencyTotals.medium += severity.medium || 0;
              closedDeficiencyTotals.low += severity.low || 0;
            }
          }
        } else {
          // For a specific tab, use the severity data for that category
          const severity = severityData[activeTab] || {
            high: 0,
            medium: 0,
            low: 0,
          };
          closedDeficiencyTotals.high = severity.high || 0;
          closedDeficiencyTotals.medium = severity.medium || 0;
          closedDeficiencyTotals.low = severity.low || 0;
        }
      }

      const totalClosedDeficiencies = filteredData.reduce(
        (sum: number, item: any) => sum + (item.accepted_by_office || 0),
        0
      );

      // update chart data
      newChartData = {
        openDeficiencies: {
          title: "Open Deficiencies",
          total: totalOpenDeficiencies,
          data: [
            {
              label: tableHeaders1?.[0] || "Overdue",
              value: totalOverdue,
              color: badgeColors?.[0] || "#d80e61",
            },
            {
              label: tableHeaders1?.[1] || "Due within 30 days",
              value: totalDueWithin30Days,
              color: badgeColors?.[1] || "#fbc02d",
            },
            {
              label: tableHeaders1?.[2] || "Others",
              value: totalOthers,
              color: badgeColors?.[2] || "#27a527",
            },
          ],
        },
        closedDeficiencies: {
          title: "Closed Deficiencies",
          total: totalClosedDeficiencies,
          data: [
            {
              label: tableHeaders2?.[0] || "High",
              value: closedDeficiencyTotals.high,
              color: badgeColors?.[0] || "#d80e61",
            },
            {
              label: tableHeaders2?.[1] || "Medium",
              value: closedDeficiencyTotals.medium,
              color: badgeColors?.[1] || "#fbc02d",
            },
            {
              label: tableHeaders2?.[2] || "Low",
              value: closedDeficiencyTotals.low,
              color: badgeColors?.[2] || "#27a527",
            },
          ],
        },
      };
    }

    return { filteredVessels: filteredData, chartData: newChartData };
  }, [
    vessels,
    activeTab,
    selectStates,
    visibleConfig.filterApplyonRenderData,
    multiVesselSelects,
    configKey,
    staticData.severityData,
    staticData.tableHeaders1,
    staticData.tableHeaders2,
    badgeColors,
  ]);

  // Use a local variable to calculate maxRange and pass it to CardGrid
  const barChartMaxRange = findMaxBarChartValue(filteredVessels);

  const handleRefresh = useCallback(() => {
    onRefresh();
    setLastUpdated(new Date());
  }, [onRefresh]);

  const handleSelectChange = useCallback(
    (index: number, newSelected: string[]) => {
      setSelectStates((prevStates) => {
        const newStates = [...prevStates];
        newStates[index] = newSelected;
        return newStates;
      });
    },
    []
  );

  const hasVesselsData = vessels && vessels.length > 0;
  const finalIsiconRenderVisible = hasVesselsData
    ? visibleConfig.IsiconRenderVisible
    : false;
  const finalIsenLargeIconVisible = hasVesselsData
    ? visibleConfig.IsenLargeIconVisible
    : false;

  const renderViewContent = () =>
    viewMode === "list" ? (
      <CardList
        vessels={filteredVessels}
        columns={columnsProp} // Pass the columns array here
        onSendEmail={onSendEmail}
        onVesselClick={onVesselClick}
        configKey={configKey}
        ga4EventTrigger={ga4EventTrigger}
        pagination={pagination}
        isFetchingNextPage={isFetchingNextPage ?? false}
        isLoading={isLoading ?? false}
        fetchNextPage={fetchNextPage}
        responsive={responsive}
        responsiveConfig={responsiveConfig}
        responsiveCardListContainerHeight={responsiveCardListContainerHeight}
        isMobileOrTablet={isMobileOrTablet}
      />
    ) : (
      <CardGrid
        vessels={filteredVessels}
        tableHeaders={staticData.tableHeaders}
        badgeColors={staticData.badgeColors}
        chartData={chartData}
        barChartMaxRange={barChartMaxRange}
        pagination={pagination}
        isFetchingNextPage={isFetchingNextPage}
        isLoading={isLoading}
        fetchNextPage={fetchNextPage}
        onSendEmail={onSendEmail}
        onVesselClick={onVesselClick}
        gridComponent={componentView?.gridComponent}
        configKey={configKey}
        ga4EventTrigger={ga4EventTrigger}
        activeTab={activeTab}
        department={department}
        isModal={isModalOpen}
        isMobileOrTablet={isMobileOrTablet}
      />
    );

  const renderModuleCore = () => (
    <>
      <CardModuleHeader
        title={title}
        viewMode={viewMode}
        isModal={isModalOpen}
        IsiconRenderVisible={finalIsiconRenderVisible && !isMobileOrTablet}
        IsenLargeIconVisible={finalIsenLargeIconVisible && !isMobileOrTablet}
        ga4EventTrigger={ga4EventTrigger}
        configKey={configKey}
        onViewModeChange={setViewMode}
        onToggleModal={() => setIsModalOpen(!isModalOpen)}
      />

      {visibleConfig.IsLastUpdatedVisible && (
        <div className="ra-last-updated-container">
          <p className="ra-last-updated-text">
            Last Updated on: {`${formatUTCDate(lastUpdated)}`}
            {visibleConfig.IsRefereshIconVisible && (
              <RotateCw onClick={handleRefresh} className="ra-refresh-icon" />
            )}
          </p>
        </div>
      )}

      {visibleConfig.IsVesselSelectVisible &&
        visibleConfig.vesselSelectPosition === "before" &&
        viewMode === "list" && (
          <CardDropdownSelectors
            multiSelects={multiVesselSelects}
            selectStates={selectStates}
            onSelectChange={handleSelectChange}
            ga4EventTrigger={ga4EventTrigger}
            configKey={configKey}
          />
        )}

      {visibleConfig.IsAlltabsVisible && (
        <CardTabs
          tabs={staticData.tabs}
          activeTab={activeTab}
          IsAllTabVisible={visibleConfig.IsAllTabVisible}
          isLoading={isLoading ?? false}
          configKey={configKey}
          ga4EventTrigger={ga4EventTrigger}
          onTabChange={handleTabChange}
        />
      )}

      {visibleConfig.IsVesselSelectVisible &&
        visibleConfig.vesselSelectPosition === "after" &&
        viewMode === "list" && (
          <CardDropdownSelectors
            multiSelects={multiVesselSelects}
            selectStates={selectStates}
            onSelectChange={handleSelectChange}
            ga4EventTrigger={ga4EventTrigger}
            configKey={configKey}
          />
        )}

      {renderViewContent()}
    </>
  );

  return (
    <>
      <div
        style={
          isMobileOrTablet && responsiveCardContainerHeight
            ? { height: responsiveCardContainerHeight }
            : {}
        }
        className={classNames("ra-vessel-card-container", `size-${sizeKey}`)}
      >
        {renderModuleCore()}
      </div>

      {isModalOpen && (
        <ModuleModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          sizeKey={sizeKey}
        >
          {renderModuleCore()}
        </ModuleModal>
      )}
    </>
  );
}
