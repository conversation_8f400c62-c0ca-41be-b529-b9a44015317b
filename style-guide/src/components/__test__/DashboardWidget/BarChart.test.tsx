import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import Bar<PERSON><PERSON> from "../../DashboardWidget/common/BarChart";

// Mock D3 library
jest.mock("d3", () => ({
  tickStep: jest.fn((min, max, count) => Math.ceil((max - min) / count)),
  range: jest.fn((start, stop, step) => {
    const result: number[] = [];
    for (let i = start; i < stop; i += step) {
      result.push(i);
    }
    return result;
  }),
  scaleLinear: jest.fn(() => ({
    domain: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    nice: jest.fn().mockReturnThis(),
    ticks: jest.fn((count) => {
      // Return mock ticks based on count
      const result: number[] = [];
      for (let i = 0; i <= count; i++) {
        result.push(i * 50); // Mock tick values
      }
      return result;
    }),
  })),
}));

// Mock useD3Chart hook
const mockUseD3Chart = {
  xScale: jest.fn((value) => value * 2), // Mock scale function
  yScale: jest.fn((value) => ({ toString: () => value })),
  barColorScale: jest.fn((key) => {
    const colors = {
      "Header 1": "#ff0000",
      "Header 2": "#00ff00",
      "Header 3": "#0000ff",
    };
    return colors[key] || "#cccccc";
  }),
  textColorScale: jest.fn((key) => {
    const colors = {
      "Header 1": "white",
      "Header 2": "black",
      "Header 3": "white",
    };
    return colors[key] || "black";
  }),
  stackedBarData: [
    {
      vesselName: "Vessel 1",
      vesselIndex: 0,
      segments: [
        {
          key: "Header 1",
          value: 10,
          x: 0,
          y: 15,
          width: 20,
          height: 25,
          vesselName: "Vessel 1",
          vesselOwnershipId: 1,
          vesselIndex: 0,
        },
        {
          key: "Header 2",
          value: 15,
          x: 21,
          y: 15,
          width: 30,
          height: 25,
          vesselName: "Vessel 1",
          vesselOwnershipId: 1,
          vesselIndex: 0,
        },
      ],
    },
    {
      vesselName: "Vessel 2",
      vesselIndex: 1,
      segments: [
        {
          key: "Header 1",
          value: 8,
          x: 0,
          y: 55,
          width: 16,
          height: 25,
          vesselName: "Vessel 2",
          vesselOwnershipId: 2,
          vesselIndex: 1,
        },
      ],
    },
  ],
  chartHeight: 90,
  totalHeight: 140,
};

jest.mock("../../DashboardWidget/hooks/useD3Chart", () => ({
  useD3Chart: jest.fn(() => mockUseD3Chart),
}));

// Mock ChartComponents
jest.mock("../../DashboardWidget/common/ChartComponents", () => ({
  ScrollableChartElements: (props: any) => (
    <g data-testid="scrollable-chart-elements">
      <text data-testid="vessels-count">{props.vessels.length}</text>
      <text data-testid="ticks-count">{props.ticks.length}</text>
    </g>
  ),
  StickyXAxis: (props: any) => (
    <div data-testid="sticky-x-axis">
      <span data-testid="axis-width">{props.width}</span>
      <span data-testid="axis-height">{props.height}</span>
      <span data-testid="axis-ticks">{props.ticks.join(",")}</span>
    </div>
  ),
  ChartTooltip: (props: any) => (
    <div
      data-testid="chart-tooltip"
      style={{
        opacity: props.isVisible ? 1 : 0,
        left: props.position.x,
        top: props.position.y,
      }}
      dangerouslySetInnerHTML={{ __html: props.content }}
    />
  ),
  ChartLegend: (props: any) => (
    <div data-testid="chart-legend">
      {props.valueHeaders.map((header: string, index: number) => (
        <div key={header} data-testid={`legend-item-${index}`}>
          <span style={{ backgroundColor: props.badgeColors[index] }}>
            {header}
          </span>
        </div>
      ))}
    </div>
  ),
}));

// Mock utility functions
jest.mock("../../DashboardWidget/util/util", () => ({
  getSurveyActiveDueTab: jest.fn(() => "active"),
}));

// Mock SCSS import
jest.mock("../../DashboardWidget/common/styles/BarChart.scss", () => ({}));

const mockVessels = [
  {
    name: "Vessel 1",
    "Header 1": 10,
    "Header 2": 15,
    "Header 3": 0,
    vessel_ownership_id: 1,
  },
  {
    name: "Vessel 2",
    "Header 1": 8,
    "Header 2": 0,
    "Header 3": 12,
    vessel_ownership_id: 2,
  },
];

const defaultProps = {
  vessels: mockVessels,
  width: 700,
  heightPerBar: 45,
  valueHeaders: ["Header 1", "Header 2", "Header 3"],
  badgeColors: ["#ff0000", "#00ff00", "#0000ff"],
  valueDomain: [0, 250] as [number, number],
  isModal: false,
  configKey: "test-config",
  activeTab: "all",
  onSendEmail: jest.fn(),
  onVesselClick: jest.fn(),
};

describe("BarChart Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("renders without crashing", () => {
    render(<BarChart {...defaultProps} />);
    expect(screen.getByTestId("scrollable-chart-elements")).toBeInTheDocument();
    expect(screen.getByTestId("sticky-x-axis")).toBeInTheDocument();
    expect(screen.getByTestId("chart-legend")).toBeInTheDocument();
    expect(screen.getByTestId("chart-tooltip")).toBeInTheDocument();
  });

  test("renders with correct container structure", () => {
    render(<BarChart {...defaultProps} />);

    const container = document.querySelector(".ra-vessel-bar-chart-container");
    expect(container).toBeInTheDocument();

    const scrollContainer = document.querySelector(".chart-scroll-container");
    expect(scrollContainer).toBeInTheDocument();

    const chartContent = document.querySelector(".chart-content");
    expect(chartContent).toBeInTheDocument();

    const mainChart = document.querySelector(".main-chart");
    expect(mainChart).toBeInTheDocument();
  });

  test("passes correct props to ScrollableChartElements", () => {
    render(<BarChart {...defaultProps} />);

    expect(screen.getByTestId("vessels-count")).toHaveTextContent("2");
  });

  test("passes correct props to StickyXAxis", () => {
    render(<BarChart {...defaultProps} />);

    expect(screen.getByTestId("axis-width")).toHaveTextContent("700");
    expect(screen.getByTestId("axis-height")).toHaveTextContent("18");
  });

  test("renders legend with correct headers and colors", () => {
    render(<BarChart {...defaultProps} />);

    expect(screen.getByTestId("legend-item-0")).toBeInTheDocument();
    expect(screen.getByTestId("legend-item-1")).toBeInTheDocument();
    expect(screen.getByTestId("legend-item-2")).toBeInTheDocument();
  });

  test("renders bar segments correctly", () => {
    render(<BarChart {...defaultProps} />);

    // Check that bar segments are rendered
    const barSubgroups = document.querySelectorAll(".bar-subgroup");
    expect(barSubgroups).toHaveLength(3); // 2 segments for vessel 1, 1 segment for vessel 2

    // Check that rectangles are rendered
    const rects = document.querySelectorAll("rect");
    expect(rects).toHaveLength(3);

    // Check that text elements are rendered
    const texts = document.querySelectorAll("text");
    expect(texts.length).toBeGreaterThan(0);
  });

  test("handles mouse over events on bar segments", () => {
    render(<BarChart {...defaultProps} />);

    const barSubgroup = document.querySelector(".bar-subgroup");
    expect(barSubgroup).toBeInTheDocument();

    // Simulate mouse over
    fireEvent.mouseOver(barSubgroup!, { clientX: 100, clientY: 200 });

    // Check tooltip becomes visible
    const tooltip = screen.getByTestId("chart-tooltip");
    expect(tooltip).toHaveStyle({ opacity: 1 });
  });

  test("handles mouse out events", () => {
    render(<BarChart {...defaultProps} />);

    const barSubgroup = document.querySelector(".bar-subgroup");

    // First trigger mouse over
    fireEvent.mouseOver(barSubgroup!, { clientX: 100, clientY: 200 });

    // Then mouse out
    fireEvent.mouseOut(barSubgroup!);

    const tooltip = screen.getByTestId("chart-tooltip");
    expect(tooltip).toHaveStyle({ opacity: 0 });
  });

  test("handles click events on bar segments", () => {
    const onVesselClick = jest.fn();
    const onSendEmail = jest.fn();

    render(
      <BarChart
        {...defaultProps}
        onVesselClick={onVesselClick}
        onSendEmail={onSendEmail}
      />
    );

    const barSubgroup = document.querySelector(".bar-subgroup");
    fireEvent.click(barSubgroup!);

    // Click handler should be called (implementation depends on configKey and activeTab)
    expect(barSubgroup).toBeInTheDocument();
  });

  test("renders with modal configuration", () => {
    const modalProps = { ...defaultProps, isModal: true, width: 800 };
    render(<BarChart {...modalProps} />);

    const chartContent = document.querySelector(".chart-content");
    expect(chartContent).toHaveStyle({ "--chart-width": "700px" }); // Modal uses fixed 700px width
  });

  test("renders with different width when not modal", () => {
    const wideProps = { ...defaultProps, isModal: false, width: 1200 };
    render(<BarChart {...wideProps} />);

    // Width should be at least 700px (minimum)
    const chartContent = document.querySelector(".chart-content");
    expect(chartContent).toBeInTheDocument();
  });

  test("renders with different heightPerBar", () => {
    const tallProps = { ...defaultProps, heightPerBar: 60 };
    render(<BarChart {...tallProps} />);

    expect(screen.getByTestId("scrollable-chart-elements")).toBeInTheDocument();
  });

  test("renders with different valueDomain", () => {
    const customDomainProps = {
      ...defaultProps,
      valueDomain: [0, 500] as [number, number],
    };
    render(<BarChart {...customDomainProps} />);

    expect(screen.getByTestId("scrollable-chart-elements")).toBeInTheDocument();
  });

  test("handles empty vessels array", () => {
    const emptyProps = { ...defaultProps, vessels: [] };
    render(<BarChart {...emptyProps} />);

    expect(screen.getByTestId("vessels-count")).toHaveTextContent("0");
    expect(screen.getByTestId("chart-legend")).toBeInTheDocument();
  });

  test("handles single vessel", () => {
    const singleVesselProps = { ...defaultProps, vessels: [mockVessels[0]] };
    render(<BarChart {...singleVesselProps} />);

    expect(screen.getByTestId("vessels-count")).toHaveTextContent("1");
  });

  test("handles vessels with all zero values", () => {
    const zeroVessels = [
      {
        name: "Zero Vessel",
        "Header 1": 0,
        "Header 2": 0,
        "Header 3": 0,
        vessel_ownership_id: 1,
      },
    ];

    const zeroProps = { ...defaultProps, vessels: zeroVessels };
    render(<BarChart {...zeroProps} />);

    expect(screen.getByTestId("vessels-count")).toHaveTextContent("1");
  });

  test("handles vessels with negative values", () => {
    const negativeVessels = [
      {
        name: "Negative Vessel",
        "Header 1": -10,
        "Header 2": 5,
        "Header 3": -3,
        vessel_ownership_id: 1,
      },
    ];

    const negativeProps = { ...defaultProps, vessels: negativeVessels };
    render(<BarChart {...negativeProps} />);

    expect(screen.getByTestId("vessels-count")).toHaveTextContent("1");
  });

  test("generates correct tooltip content", () => {
    render(<BarChart {...defaultProps} />);

    const barSubgroup = document.querySelector(".bar-subgroup");
    fireEvent.mouseOver(barSubgroup!, { clientX: 100, clientY: 200 });

    const tooltip = screen.getByTestId("chart-tooltip");
    expect(tooltip.innerHTML).toContain("tooltip-content");
    expect(tooltip.innerHTML).toContain("tooltip-title");
  });

  test("applies correct tooltip badge colors", () => {
    render(<BarChart {...defaultProps} />);

    const barSubgroup = document.querySelector(".bar-subgroup");
    fireEvent.mouseOver(barSubgroup!, { clientX: 100, clientY: 200 });

    const tooltip = screen.getByTestId("chart-tooltip");
    // Should contain tooltip color class based on segment color
    expect(tooltip.innerHTML).toContain("tooltip-color");
  });

  test("handles different configKey values", () => {
    const configProps = { ...defaultProps, configKey: "survey-config" };
    render(<BarChart {...configProps} />);

    expect(screen.getByTestId("scrollable-chart-elements")).toBeInTheDocument();
  });

  test("handles different activeTab values", () => {
    const tabProps = { ...defaultProps, activeTab: "active" };
    render(<BarChart {...tabProps} />);

    expect(screen.getByTestId("scrollable-chart-elements")).toBeInTheDocument();
  });

  test("handles undefined configKey and activeTab", () => {
    const undefinedProps = {
      ...defaultProps,
      configKey: undefined,
      activeTab: undefined,
    };
    render(<BarChart {...undefinedProps} />);

    expect(screen.getByTestId("scrollable-chart-elements")).toBeInTheDocument();
  });

  test("renders with different badge colors", () => {
    const colorProps = {
      ...defaultProps,
      badgeColors: ["#purple", "#orange", "#cyan", "#magenta"],
    };
    render(<BarChart {...colorProps} />);

    expect(screen.getByTestId("chart-legend")).toBeInTheDocument();
  });

  test("renders with different value headers", () => {
    const headerProps = {
      ...defaultProps,
      valueHeaders: ["Status A", "Status B", "Status C", "Status D"],
    };
    render(<BarChart {...headerProps} />);

    expect(screen.getByTestId("chart-legend")).toBeInTheDocument();
  });

  test("handles large datasets", () => {
    const largeVessels = Array.from({ length: 50 }, (_, i) => ({
      name: `Vessel ${i + 1}`,
      "Header 1": Math.floor(Math.random() * 100),
      "Header 2": Math.floor(Math.random() * 100),
      "Header 3": Math.floor(Math.random() * 100),
      vessel_ownership_id: i + 1,
    }));

    const largeProps = { ...defaultProps, vessels: largeVessels };
    render(<BarChart {...largeProps} />);

    expect(screen.getByTestId("vessels-count")).toHaveTextContent("50");
  });

  test("handles vessels with missing properties", () => {
    const incompleteVessels = [
      {
        name: "Incomplete Vessel",
        "Header 1": 10,
        // Missing Header 2 and Header 3
      },
    ];

    const incompleteProps = { ...defaultProps, vessels: incompleteVessels };
    render(<BarChart {...incompleteProps} />);

    expect(screen.getByTestId("vessels-count")).toHaveTextContent("1");
  });

  test("maintains component structure across re-renders", () => {
    const { rerender } = render(<BarChart {...defaultProps} />);

    expect(screen.getByTestId("scrollable-chart-elements")).toBeInTheDocument();
    expect(screen.getByTestId("sticky-x-axis")).toBeInTheDocument();

    // Re-render with different props
    rerender(<BarChart {...defaultProps} heightPerBar={60} />);

    expect(screen.getByTestId("scrollable-chart-elements")).toBeInTheDocument();
    expect(screen.getByTestId("sticky-x-axis")).toBeInTheDocument();
  });

  test("calls useD3Chart hook with correct parameters", () => {
    const { useD3Chart } = require("../../DashboardWidget/hooks/useD3Chart");

    render(<BarChart {...defaultProps} />);

    expect(useD3Chart).toHaveBeenCalledWith({
      vessels: mockVessels,
      valueHeaders: ["Header 1", "Header 2", "Header 3"],
      badgeColors: ["#ff0000", "#00ff00", "#0000ff"],
      valueDomain: [0, 400],
      chartWidth: 700,
      heightPerBar: 45,
      isMobileOrTablet: false,
      margin: { top: 15, right: 15, bottom: 18, left: 90 },
      tickDiff: 50,
    });
  });

  test("handles container width changes", () => {
    render(<BarChart {...defaultProps} />);

    // Component should handle container width changes internally
    expect(screen.getByTestId("scrollable-chart-elements")).toBeInTheDocument();
  });

  test("handles click events for surveys-certificates config", () => {
    // Mock window.open
    const mockWindowOpen = jest.fn();
    Object.defineProperty(window, "open", {
      value: mockWindowOpen,
      writable: true,
    });

    const surveysProps = {
      ...defaultProps,
      configKey: "surveys-certificates",
      activeTab: "Active",
    };

    render(<BarChart {...surveysProps} />);

    const barSubgroup = document.querySelector(".bar-subgroup");
    fireEvent.click(barSubgroup!);

    expect(mockWindowOpen).toHaveBeenCalledWith(
      "vessel/ownership/details/1/certificates?activeTab=active&activeDueTab=active",
      "_blank"
    );
  });

  test("handles click events for surveys-certificates config with All tab", () => {
    // Mock window.open
    const mockWindowOpen = jest.fn();
    Object.defineProperty(window, "open", {
      value: mockWindowOpen,
      writable: true,
    });

    const surveysProps = {
      ...defaultProps,
      configKey: "surveys-certificates",
      activeTab: "All",
    };

    render(<BarChart {...surveysProps} />);

    const barSubgroup = document.querySelector(".bar-subgroup");
    fireEvent.click(barSubgroup!);

    expect(mockWindowOpen).toHaveBeenCalledWith(
      "vessel/ownership/details/1/certificates?activeTab=all_types&activeDueTab=active",
      "_blank"
    );
  });

  test("does not open window for non-surveys-certificates config", () => {
    // Mock window.open
    const mockWindowOpen = jest.fn();
    Object.defineProperty(window, "open", {
      value: mockWindowOpen,
      writable: true,
    });

    const nonSurveysProps = {
      ...defaultProps,
      configKey: "other-config",
    };

    render(<BarChart {...nonSurveysProps} />);

    const barSubgroup = document.querySelector(".bar-subgroup");
    fireEvent.click(barSubgroup!);

    expect(mockWindowOpen).not.toHaveBeenCalled();
  });

  test("handles mouse move events when tooltip is visible", () => {
    render(<BarChart {...defaultProps} />);

    const barSubgroup = document.querySelector(".bar-subgroup");

    // First trigger mouse over to make tooltip visible
    fireEvent.mouseOver(barSubgroup!, { clientX: 100, clientY: 200 });

    // Then trigger mouse move
    fireEvent.mouseMove(barSubgroup!, { clientX: 150, clientY: 250 });

    const tooltip = screen.getByTestId("chart-tooltip");
    expect(tooltip).toHaveStyle({
      opacity: 1,
      left: "170px", // clientX + 20
      top: "250px", // clientY
    });
  });

  test("does not update tooltip position when not visible", () => {
    render(<BarChart {...defaultProps} />);

    const barSubgroup = document.querySelector(".bar-subgroup");

    // Trigger mouse move without making tooltip visible first
    fireEvent.mouseMove(barSubgroup!, { clientX: 150, clientY: 250 });

    const tooltip = screen.getByTestId("chart-tooltip");
    expect(tooltip).toHaveStyle({ opacity: 0 });
  });

  test("generates ticks correctly with different value domains", () => {
    const customDomainProps = {
      ...defaultProps,
      valueDomain: [0, 100] as [number, number],
    };

    render(<BarChart {...customDomainProps} />);

    // Ticks should be generated based on the domain
    const axisElement = screen.getByTestId("axis-ticks");
    expect(axisElement).toBeInTheDocument();
  });

  test("handles tooltip with different badge colors", () => {
    const colorProps = {
      ...defaultProps,
      badgeColors: ["#d80e61", "#fbc02d", "#27a527"], // Colors that map to specific tooltip classes
    };

    render(<BarChart {...colorProps} />);

    const barSubgroup = document.querySelector(".bar-subgroup");
    fireEvent.mouseOver(barSubgroup!, { clientX: 100, clientY: 200 });

    const tooltip = screen.getByTestId("chart-tooltip");
    expect(tooltip.innerHTML).toContain("tooltip-color");
  });

  test("handles tooltip with unmapped badge colors", () => {
    const colorProps = {
      ...defaultProps,
      badgeColors: ["#123456", "#789abc", "#def012"], // Colors not in the badge color map
    };

    render(<BarChart {...colorProps} />);

    const barSubgroup = document.querySelector(".bar-subgroup");
    fireEvent.mouseOver(barSubgroup!, { clientX: 100, clientY: 200 });

    const tooltip = screen.getByTestId("chart-tooltip");
    expect(tooltip.innerHTML).toContain("tooltip-content");
  });

  test("applies correct CSS custom properties for chart width", () => {
    render(<BarChart {...defaultProps} />);

    const chartContent = document.querySelector(".chart-content");
    expect(chartContent).toHaveStyle({ "--chart-width": "700px" });
  });

  test("handles vessels with undefined values for headers", () => {
    const undefinedVessels = [
      {
        name: "Undefined Vessel",
        "Header 1": undefined,
        "Header 2": 10,
        "Header 3": undefined,
        vessel_ownership_id: 1,
      },
    ];

    const undefinedProps = { ...defaultProps, vessels: undefinedVessels };
    render(<BarChart {...undefinedProps} />);

    expect(screen.getByTestId("vessels-count")).toHaveTextContent("1");
  });

  test("handles vessels with null values for headers", () => {
    const nullVessels = [
      {
        name: "Null Vessel",
        "Header 1": null,
        "Header 2": 15,
        "Header 3": null,
        vessel_ownership_id: 1,
      },
    ];

    const nullProps = { ...defaultProps, vessels: nullVessels };
    render(<BarChart {...nullProps} />);

    expect(screen.getByTestId("vessels-count")).toHaveTextContent("1");
  });
});
