import React from "react";
import { render } from "@testing-library/react";
import "@testing-library/jest-dom";

// Create mock objects that will be available to tests

// Expose mocks for assertions
let mockAxis: any, mockSelection: any, mockD3: any;
// Mock the entire d3 package and its submodules
jest.mock("d3-selection", () => {
  mockSelection = {
    call: jest.fn().mockReturnThis(),
    selectAll: jest.fn().mockReturnThis(),
    attr: jest.fn().mockReturnThis(),
    text: jest.fn().mockReturnThis(),
    append: jest.fn().mockReturnThis(),
    remove: jest.fn().mockReturnThis(),
    node: jest.fn(() => ({
      getComputedTextLength: jest.fn(() => 100),
    })),
    each: jest.fn(function(callback) {
      callback.call({
        text: jest.fn(() => "Test Vessel Name"),
        attr: jest.fn(() => "0"),
      });
    }),
  };
  return {
    select: jest.fn(() => mockSelection),
  };
});
jest.mock("d3-axis", () => {
  mockAxis = {
    tickFormat: jest.fn().mockReturnThis(),
    tickSize: jest.fn().mockReturnThis(),
    tickValues: jest.fn().mockReturnThis(),
  };
  return {
    axisLeft: jest.fn(() => mockAxis),
    axisBottom: jest.fn(() => mockAxis),
  };
});
jest.mock("d3-transition", () => ({
  transition: jest.fn(() => ({})),
}));
jest.mock("d3-brush", () => ({
  brushX: jest.fn(() => ({})),
}));
jest.mock("d3", () => {
  // Compose a mock d3 object with all needed submodules
  const selection = {
    call: jest.fn().mockReturnThis(),
    selectAll: jest.fn().mockReturnThis(),
    attr: jest.fn().mockReturnThis(),
    text: jest.fn().mockReturnThis(),
    append: jest.fn().mockReturnThis(),
    remove: jest.fn().mockReturnThis(),
    node: jest.fn(() => ({
      getComputedTextLength: jest.fn(() => 100),
    })),
    each: jest.fn(function(callback) {
      callback.call({
        text: jest.fn(() => "Test Vessel Name"),
        attr: jest.fn(() => "0"),
      });
    }),
  };
  const axis = {
    tickFormat: jest.fn().mockReturnThis(),
    tickSize: jest.fn().mockReturnThis(),
    tickValues: jest.fn().mockReturnThis(),
  };
  mockSelection = selection;
  mockAxis = axis;
  mockD3 = {
    select: jest.fn(() => selection),
    axisLeft: jest.fn(() => axis),
    axisBottom: jest.fn(() => axis),
    transition: jest.fn(() => ({})),
    brushX: jest.fn(() => ({})),
  };
  return mockD3;
});

// Import components AFTER mocking D3
import {
  ScrollableChartElements,
  StickyXAxis,
  ChartTooltip,
  ChartLegend,
} from "../../DashboardWidget/common/ChartComponents";

// Create mock scales for tests
const mockYScale = jest.fn() as any;
mockYScale.mockImplementation((value: string) => {
  const index = parseInt(value, 10);
  return 50 + index * 45; // Mock y position calculation
});
mockYScale.bandwidth = jest.fn(() => 35);

const mockXScale = jest.fn() as any;
mockXScale.mockImplementation((value: number) => value * 2);

const mockVessels = [
  { name: "Vessel Alpha", vessel_ownership_id: 1 },
  { name: "Vessel Beta", vessel_ownership_id: 2 },
  { name: "Vessel Gamma", vessel_ownership_id: 3 },
];

const mockTicks = [0, 25, 50, 75, 100, 125, 150];

const mockMargin = { top: 15, right: 15, bottom: 40, left: 90 };

describe("ChartComponents", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("ScrollableChartElements", () => {
    const defaultProps = {
      yScale: mockYScale,
      xScale: mockXScale,
      height: 300,
      margin: mockMargin,
      vessels: mockVessels,
      ticks: mockTicks,
    };

    test("renders without crashing", () => {
      render(
        <svg>
          <ScrollableChartElements {...defaultProps} />
        </svg>
      );

      expect(document.querySelector(".x-grid")).toBeInTheDocument();
      expect(document.querySelector(".y-axis")).toBeInTheDocument();
    });

    test("renders with correct transform attributes", () => {
      render(
        <svg>
          <ScrollableChartElements {...defaultProps} />
        </svg>
      );

      const xGrid = document.querySelector(".x-grid");
      const yAxis = document.querySelector(".y-axis");

      expect(xGrid).toHaveAttribute(
        "transform",
        `translate(${mockMargin.left}, ${defaultProps.height + mockMargin.top})`
      );
      expect(yAxis).toHaveAttribute(
        "transform",
        `translate(${mockMargin.left}, 0)`
      );
    });

    test("calls D3 axis functions on mount", () => {
      render(
        <svg>
          <ScrollableChartElements {...defaultProps} />
        </svg>
      );

      expect(mockD3.axisLeft).toHaveBeenCalledWith(mockYScale);
      expect(mockD3.axisBottom).toHaveBeenCalledWith(mockXScale);
      expect(mockAxis.tickFormat).toHaveBeenCalled();
      expect(mockAxis.tickSize).toHaveBeenCalled();
      expect(mockAxis.tickValues).toHaveBeenCalledWith(mockTicks);
    });

    test("updates when props change", () => {
      const { rerender } = render(
        <svg>
          <ScrollableChartElements {...defaultProps} />
        </svg>
      );

      const newTicks = [0, 50, 100, 150, 200];
      rerender(
        <svg>
          <ScrollableChartElements {...defaultProps} ticks={newTicks} />
        </svg>
      );

      expect(mockAxis.tickValues).toHaveBeenCalledWith(newTicks);
    });

    test("handles empty vessels array", () => {
      const emptyVesselsProps = { ...defaultProps, vessels: [] };

      expect(() => {
        render(
          <svg>
            <ScrollableChartElements {...emptyVesselsProps} />
          </svg>
        );
      }).not.toThrow();
    });

    test("handles different margin values", () => {
      const customMargin = { top: 20, right: 20, bottom: 50, left: 100 };
      const customProps = { ...defaultProps, margin: customMargin };

      render(
        <svg>
          <ScrollableChartElements {...customProps} />
        </svg>
      );

      const xGrid = document.querySelector(".x-grid");
      expect(xGrid).toHaveAttribute(
        "transform",
        `translate(${customMargin.left}, ${defaultProps.height +
          customMargin.top})`
      );
    });

    test("handles different height values", () => {
      const customHeight = 500;
      const customProps = { ...defaultProps, height: customHeight };

      render(
        <svg>
          <ScrollableChartElements {...customProps} />
        </svg>
      );

      const xGrid = document.querySelector(".x-grid");
      expect(xGrid).toHaveAttribute(
        "transform",
        `translate(${mockMargin.left}, ${customHeight + mockMargin.top})`
      );
    });

    test("handles vessel names with special characters in tickFormat", () => {
      const specialVessels = [
        { name: "Vessel & Company", vessel_ownership_id: 1 },
        { name: "Vessel-With-Dashes", vessel_ownership_id: 2 },
        { name: "Vessel_With_Underscores", vessel_ownership_id: 3 },
      ];

      render(
        <svg>
          <ScrollableChartElements {...defaultProps} vessels={specialVessels} />
        </svg>
      );

      expect(mockAxis.tickFormat).toHaveBeenCalled();
    });

    test("handles negative height values", () => {
      const negativeHeightProps = { ...defaultProps, height: -100 };

      expect(() => {
        render(
          <svg>
            <ScrollableChartElements {...negativeHeightProps} />
          </svg>
        );
      }).not.toThrow();
    });

    test("handles zero height", () => {
      const zeroHeightProps = { ...defaultProps, height: 0 };

      render(
        <svg>
          <ScrollableChartElements {...zeroHeightProps} />
        </svg>
      );

      const xGrid = document.querySelector(".x-grid");
      expect(xGrid).toHaveAttribute(
        "transform",
        `translate(${mockMargin.left}, ${0 + mockMargin.top})`
      );
    });
  });

  describe("StickyXAxis", () => {
    const defaultProps = {
      xScale: mockXScale,
      width: 700,
      height: 40,
      margin: { left: 90 },
      ticks: mockTicks,
    };

    test("renders without crashing", () => {
      render(<StickyXAxis {...defaultProps} />);

      expect(document.querySelector(".x-axis-container")).toBeInTheDocument();
      expect(document.querySelector(".x-axis")).toBeInTheDocument();
    });

    test("renders SVG with correct dimensions", () => {
      render(<StickyXAxis {...defaultProps} />);

      const svg = document.querySelector("svg");
      expect(svg).toHaveAttribute("width", "700");
      expect(svg).toHaveAttribute("height", "40");
    });

    test("renders axis group with correct transform", () => {
      render(<StickyXAxis {...defaultProps} />);

      const axisGroup = document.querySelector(".x-axis");
      expect(axisGroup).toHaveAttribute(
        "transform",
        `translate(${defaultProps.margin.left}, 0)`
      );
    });

    test("calls D3 axis functions on mount", () => {
      render(<StickyXAxis {...defaultProps} />);

      expect(mockD3.axisBottom).toHaveBeenCalledWith(mockXScale);
      expect(mockAxis.tickValues).toHaveBeenCalledWith(mockTicks);
      expect(mockSelection.selectAll).toHaveBeenCalledWith(".tick line");
      expect(mockSelection.remove).toHaveBeenCalled();
    });

    test("updates when xScale changes", () => {
      const { rerender } = render(<StickyXAxis {...defaultProps} />);

      const newXScale = jest.fn() as any;
      newXScale.mockImplementation((value: number) => value * 3);

      rerender(<StickyXAxis {...defaultProps} xScale={newXScale} />);

      expect(mockD3.axisBottom).toHaveBeenCalledWith(newXScale);
    });

    test("updates when ticks change", () => {
      const { rerender } = render(<StickyXAxis {...defaultProps} />);

      const newTicks = [0, 100, 200, 300];
      rerender(<StickyXAxis {...defaultProps} ticks={newTicks} />);

      expect(mockAxis.tickValues).toHaveBeenCalledWith(newTicks);
    });

    test("handles different width values", () => {
      const customProps = { ...defaultProps, width: 1000 };
      render(<StickyXAxis {...customProps} />);

      const svg = document.querySelector("svg");
      expect(svg).toHaveAttribute("width", "1000");
    });

    test("handles different height values", () => {
      const customProps = { ...defaultProps, height: 60 };
      render(<StickyXAxis {...customProps} />);

      const svg = document.querySelector("svg");
      expect(svg).toHaveAttribute("height", "60");
    });

    test("handles different margin values", () => {
      const customMargin = { left: 120 };
      const customProps = { ...defaultProps, margin: customMargin };

      render(<StickyXAxis {...customProps} />);

      const axisGroup = document.querySelector(".x-axis");
      expect(axisGroup).toHaveAttribute(
        "transform",
        `translate(${customMargin.left}, 0)`
      );
    });

    test("handles empty ticks array", () => {
      const emptyTicksProps = { ...defaultProps, ticks: [] };

      expect(() => {
        render(<StickyXAxis {...emptyTicksProps} />);
      }).not.toThrow();

      expect(mockAxis.tickValues).toHaveBeenCalledWith([]);
    });

    test("handles single tick", () => {
      const singleTickProps = { ...defaultProps, ticks: [50] };
      render(<StickyXAxis {...singleTickProps} />);

      expect(mockAxis.tickValues).toHaveBeenCalledWith([50]);
    });

    test("handles large number of ticks", () => {
      const manyTicks = Array.from({ length: 20 }, (_, i) => i * 10);
      const manyTicksProps = { ...defaultProps, ticks: manyTicks };

      render(<StickyXAxis {...manyTicksProps} />);

      expect(mockAxis.tickValues).toHaveBeenCalledWith(manyTicks);
    });

    test("handles negative tick values", () => {
      const negativeTicks = [-100, -50, 0, 50, 100];
      const negativeTicksProps = { ...defaultProps, ticks: negativeTicks };

      render(<StickyXAxis {...negativeTicksProps} />);

      expect(mockAxis.tickValues).toHaveBeenCalledWith(negativeTicks);
    });

    test("re-renders correctly when all props change", () => {
      const { rerender } = render(<StickyXAxis {...defaultProps} />);

      const newProps = {
        xScale: jest.fn() as any,
        width: 800,
        height: 50,
        margin: { left: 100 },
        ticks: [0, 25, 50, 75, 100],
      };

      rerender(<StickyXAxis {...newProps} />);

      const svg = document.querySelector("svg");
      expect(svg).toHaveAttribute("width", "800");
      expect(svg).toHaveAttribute("height", "50");
    });

    test("handles undefined or null xScale", () => {
      const nullScaleProps = { ...defaultProps, xScale: null as any };

      // This should not crash, though it might not render correctly
      expect(() => {
        render(<StickyXAxis {...nullScaleProps} />);
      }).not.toThrow();
    });

    test("handles zero width and height", () => {
      const zeroSizeProps = {
        ...defaultProps,
        width: 0,
        height: 0,
      };

      render(<StickyXAxis {...zeroSizeProps} />);

      const svg = document.querySelector("svg");
      expect(svg).toHaveAttribute("width", "0");
      expect(svg).toHaveAttribute("height", "0");
    });

    test("handles negative width and height", () => {
      const negativeSizeProps = {
        ...defaultProps,
        width: -100,
        height: -50,
      };

      render(<StickyXAxis {...negativeSizeProps} />);

      const svg = document.querySelector("svg");
      expect(svg).toHaveAttribute("width", "-100");
      expect(svg).toHaveAttribute("height", "-50");
    });
  });

  describe("ChartTooltip", () => {
    const defaultProps = {
      content: '<div class="tooltip-content"><b>Test Content</b></div>',
      position: { x: 100, y: 200 },
      isVisible: true,
    };

    test("renders without crashing", () => {
      render(<ChartTooltip {...defaultProps} />);

      expect(document.querySelector(".custom-tooltip")).toBeInTheDocument();
    });

    test("renders with correct visibility when visible", () => {
      render(<ChartTooltip {...defaultProps} />);

      const tooltip = document.querySelector(".custom-tooltip");
      expect(tooltip).toHaveClass("custom-tooltip--visible");
    });

    test("renders with correct visibility when hidden", () => {
      const hiddenProps = { ...defaultProps, isVisible: false };
      render(<ChartTooltip {...hiddenProps} />);

      const tooltip = document.querySelector(".custom-tooltip");
      expect(tooltip).not.toHaveClass("custom-tooltip--visible");
    });

    test("renders with correct position", () => {
      render(<ChartTooltip {...defaultProps} />);

      const tooltip = document.querySelector(".custom-tooltip");
      expect(tooltip).toHaveStyle({
        "--tooltip-left": "100px",
        "--tooltip-top": "200px",
      });
    });

    test("renders with different position values", () => {
      const customPositionProps = {
        ...defaultProps,
        position: { x: 300, y: 400 },
      };
      render(<ChartTooltip {...customPositionProps} />);

      const tooltip = document.querySelector(".custom-tooltip");
      expect(tooltip).toHaveStyle({
        "--tooltip-left": "300px",
        "--tooltip-top": "400px",
      });
    });

    test("renders HTML content correctly", () => {
      render(<ChartTooltip {...defaultProps} />);

      const tooltip = document.querySelector(".custom-tooltip");
      expect(tooltip?.innerHTML).toBe(
        '<div class="tooltip-content"><b>Test Content</b></div>'
      );
    });

    test("handles empty content", () => {
      const emptyContentProps = { ...defaultProps, content: "" };
      render(<ChartTooltip {...emptyContentProps} />);

      const tooltip = document.querySelector(".custom-tooltip");
      expect(tooltip?.innerHTML).toBe("");
    });

    test("handles complex HTML content", () => {
      const complexContent = `
        <div class="tooltip-content">
          <div class="tooltip-title"><b>Vessel Name</b></div>
          <div class="tooltip-row">
            <span class="tooltip-color tooltip-color--pink"></span>
            <span>25</span>
          </div>
        </div>
      `;
      const complexProps = { ...defaultProps, content: complexContent };
      render(<ChartTooltip {...complexProps} />);

      const tooltip = document.querySelector(".custom-tooltip");
      expect(tooltip?.innerHTML).toBe(complexContent);
    });

    test("handles zero position values", () => {
      const zeroPositionProps = {
        ...defaultProps,
        position: { x: 0, y: 0 },
      };
      render(<ChartTooltip {...zeroPositionProps} />);

      const tooltip = document.querySelector(".custom-tooltip");
      expect(tooltip).toHaveStyle({
        "--tooltip-left": "0px",
        "--tooltip-top": "0px",
      });
    });

    test("handles negative position values", () => {
      const negativePositionProps = {
        ...defaultProps,
        position: { x: -50, y: -100 },
      };
      render(<ChartTooltip {...negativePositionProps} />);

      const tooltip = document.querySelector(".custom-tooltip");
      expect(tooltip).toHaveStyle({
        "--tooltip-left": "-50px",
        "--tooltip-top": "-100px",
      });
    });

    test("re-renders when props change", () => {
      const { rerender } = render(<ChartTooltip {...defaultProps} />);

      const newProps = {
        ...defaultProps,
        content: "<div>New Content</div>",
        position: { x: 500, y: 600 },
        isVisible: false,
      };
      rerender(<ChartTooltip {...newProps} />);

      const tooltip = document.querySelector(".custom-tooltip");
      expect(tooltip?.innerHTML).toBe("<div>New Content</div>");
      expect(tooltip).toHaveStyle({
        "--tooltip-left": "500px",
        "--tooltip-top": "600px",
      });
      expect(tooltip).not.toHaveClass("custom-tooltip--visible");
    });

    test("handles null or undefined content", () => {
      const nullContentProps = {
        ...defaultProps,
        content: null as any,
      };

      render(<ChartTooltip {...nullContentProps} />);

      const tooltip = document.querySelector(".custom-tooltip");
      // Should handle null content gracefully
      expect(tooltip).toBeInTheDocument();
    });

    test("handles extremely large position values", () => {
      const largePositionProps = {
        ...defaultProps,
        position: { x: 9999, y: 9999 },
      };

      render(<ChartTooltip {...largePositionProps} />);

      const tooltip = document.querySelector(".custom-tooltip");
      expect(tooltip).toHaveStyle({
        "--tooltip-left": "9999px",
        "--tooltip-top": "9999px",
      });
    });

    test("handles decimal position values", () => {
      const decimalPositionProps = {
        ...defaultProps,
        position: { x: 123.456, y: 789.012 },
      };

      render(<ChartTooltip {...decimalPositionProps} />);

      const tooltip = document.querySelector(".custom-tooltip");
      expect(tooltip).toHaveStyle({
        "--tooltip-left": "123.456px",
        "--tooltip-top": "789.012px",
      });
    });
  });

  describe("ChartLegend", () => {
    const defaultProps = {
      valueHeaders: ["Header 1", "Header 2", "Header 3"],
      badgeColors: ["#ff0000", "#00ff00", "#0000ff"],
    };

    test("renders without crashing", () => {
      render(<ChartLegend {...defaultProps} />);

      expect(
        document.querySelector(".vessel-bar-chart-legend")
      ).toBeInTheDocument();
    });

    test("renders correct number of legend items", () => {
      render(<ChartLegend {...defaultProps} />);

      const legendItems = document.querySelectorAll(".legend-item");
      expect(legendItems).toHaveLength(3);
    });

    test("renders legend items with correct labels", () => {
      render(<ChartLegend {...defaultProps} />);

      const legendLabels = document.querySelectorAll(".legend-label");
      expect(legendLabels[0]).toHaveTextContent("Header 1");
      expect(legendLabels[1]).toHaveTextContent("Header 2");
      expect(legendLabels[2]).toHaveTextContent("Header 3");
    });

    test("renders legend items with correct colors", () => {
      render(<ChartLegend {...defaultProps} />);

      const legendColors = document.querySelectorAll(".legend-color");
      expect(legendColors[0]).toHaveStyle({ "--legend-color": "#ff0000" });
      expect(legendColors[1]).toHaveStyle({ "--legend-color": "#00ff00" });
      expect(legendColors[2]).toHaveStyle({ "--legend-color": "#0000ff" });
    });

    test("handles empty arrays", () => {
      const emptyProps = { valueHeaders: [], badgeColors: [] };
      render(<ChartLegend {...emptyProps} />);

      const legendItems = document.querySelectorAll(".legend-item");
      expect(legendItems).toHaveLength(0);
    });

    test("handles single item", () => {
      const singleItemProps = {
        valueHeaders: ["Single Header"],
        badgeColors: ["#purple"],
      };
      render(<ChartLegend {...singleItemProps} />);

      const legendItems = document.querySelectorAll(".legend-item");
      expect(legendItems).toHaveLength(1);

      const legendLabel = document.querySelector(".legend-label");
      const legendColor = document.querySelector(".legend-color");
      expect(legendLabel).toHaveTextContent("Single Header");
      expect(legendColor).toHaveStyle({ "--legend-color": "#purple" });
    });

    test("handles mismatched array lengths", () => {
      const mismatchedProps = {
        valueHeaders: ["Header 1", "Header 2", "Header 3"],
        badgeColors: ["#red", "#green"], // Only 2 colors for 3 headers
      };
      render(<ChartLegend {...mismatchedProps} />);

      const legendItems = document.querySelectorAll(".legend-item");
      expect(legendItems).toHaveLength(3); // Should render all headers

      const legendColors = document.querySelectorAll(".legend-color");
      expect(legendColors[0]).toHaveStyle({ "--legend-color": "#red" });
      expect(legendColors[1]).toHaveStyle({ "--legend-color": "#green" });
      expect(legendColors[2]).toHaveStyle({ "--legend-color": undefined }); // undefined color
    });

    test("handles long header names", () => {
      const longHeaderProps = {
        valueHeaders: ["Very Long Header Name That Might Wrap", "Short"],
        badgeColors: ["#blue", "#yellow"],
      };
      render(<ChartLegend {...longHeaderProps} />);

      const legendLabels = document.querySelectorAll(".legend-label");
      expect(legendLabels[0]).toHaveTextContent(
        "Very Long Header Name That Might Wrap"
      );
      expect(legendLabels[1]).toHaveTextContent("Short");
    });

    test("handles special characters in headers", () => {
      const specialCharProps = {
        valueHeaders: [
          "Header & Special",
          "Header-With-Dashes",
          "Header_With_Underscores",
        ],
        badgeColors: ["#cyan", "#magenta", "#orange"],
      };
      render(<ChartLegend {...specialCharProps} />);

      const legendLabels = document.querySelectorAll(".legend-label");
      expect(legendLabels[0]).toHaveTextContent("Header & Special");
      expect(legendLabels[1]).toHaveTextContent("Header-With-Dashes");
      expect(legendLabels[2]).toHaveTextContent("Header_With_Underscores");
    });

    test("handles different color formats", () => {
      const colorFormatProps = {
        valueHeaders: ["RGB", "RGBA", "Named", "HSL"],
        badgeColors: [
          "rgb(255, 0, 0)",
          "rgba(0, 255, 0, 0.5)",
          "blue",
          "hsl(240, 100%, 50%)",
        ],
      };
      render(<ChartLegend {...colorFormatProps} />);

      const legendColors = document.querySelectorAll(".legend-color");
      expect(legendColors[0]).toHaveStyle({
        "--legend-color": "rgb(255, 0, 0)",
      });
      expect(legendColors[1]).toHaveStyle({
        "--legend-color": "rgba(0, 255, 0, 0.5)",
      });
      expect(legendColors[2]).toHaveStyle({
        "--legend-color": "blue",
      });
      expect(legendColors[3]).toHaveStyle({
        "--legend-color": "hsl(240, 100%, 50%)",
      });
    });

    test("re-renders when props change", () => {
      const { rerender } = render(<ChartLegend {...defaultProps} />);

      const newProps = {
        valueHeaders: ["New Header 1", "New Header 2"],
        badgeColors: ["#pink", "#teal"],
      };
      rerender(<ChartLegend {...newProps} />);

      const legendItems = document.querySelectorAll(".legend-item");
      expect(legendItems).toHaveLength(2);

      const legendLabels = document.querySelectorAll(".legend-label");
      expect(legendLabels[0]).toHaveTextContent("New Header 1");
      expect(legendLabels[1]).toHaveTextContent("New Header 2");
    });

    test("handles arrays with null or undefined values", () => {
      const mixedArraysProps = {
        valueHeaders: ["Header 1", null, "Header 3"] as any,
        badgeColors: ["#ff0000", undefined, "#0000ff"] as any,
      };

      render(<ChartLegend {...mixedArraysProps} />);

      const legendItems = document.querySelectorAll(".legend-item");
      expect(legendItems).toHaveLength(3);
    });

    test("handles empty strings in valueHeaders", () => {
      const emptyStringProps = {
        valueHeaders: ["", "Header 2", ""],
        badgeColors: ["#ff0000", "#00ff00", "#0000ff"],
      };

      render(<ChartLegend {...emptyStringProps} />);

      const legendLabels = document.querySelectorAll(".legend-label");
      expect(legendLabels[0]).toHaveTextContent("");
      expect(legendLabels[1]).toHaveTextContent("Header 2");
      expect(legendLabels[2]).toHaveTextContent("");
    });

    test("handles invalid color values", () => {
      const invalidColorProps = {
        valueHeaders: ["Header 1", "Header 2"],
        badgeColors: ["invalid-color", "also-invalid"],
      };

      render(<ChartLegend {...invalidColorProps} />);

      const legendColors = document.querySelectorAll(".legend-color");
      expect(legendColors[0]).toHaveStyle({
        "--legend-color": "invalid-color",
      });
      expect(legendColors[1]).toHaveStyle({ "--legend-color": "also-invalid" });
    });
  });

  describe("useEffect dependency handling", () => {
    test("ScrollableChartElements re-renders when vessels array changes", () => {
      const { rerender } = render(
        <svg>
          <ScrollableChartElements
            yScale={mockYScale}
            xScale={mockXScale}
            height={300}
            margin={mockMargin}
            vessels={mockVessels}
            ticks={mockTicks}
          />
        </svg>
      );

      const newVessels = [
        { name: "New Vessel 1", vessel_ownership_id: 4 },
        { name: "New Vessel 2", vessel_ownership_id: 5 },
      ];

      rerender(
        <svg>
          <ScrollableChartElements
            yScale={mockYScale}
            xScale={mockXScale}
            height={300}
            margin={mockMargin}
            vessels={newVessels}
            ticks={mockTicks}
          />
        </svg>
      );

      expect(mockAxis.tickFormat).toHaveBeenCalled();
    });

    test("ScrollableChartElements handles margin object changes", () => {
      const { rerender } = render(
        <svg>
          <ScrollableChartElements
            yScale={mockYScale}
            xScale={mockXScale}
            height={300}
            margin={mockMargin}
            vessels={mockVessels}
            ticks={mockTicks}
          />
        </svg>
      );

      const newMargin = { top: 20, right: 20, bottom: 50, left: 100 };

      rerender(
        <svg>
          <ScrollableChartElements
            yScale={mockYScale}
            xScale={mockXScale}
            height={300}
            margin={newMargin}
            vessels={mockVessels}
            ticks={mockTicks}
          />
        </svg>
      );

      // Should re-render with new margin values
      const xGrid = document.querySelector(".x-grid");
      expect(xGrid).toHaveAttribute(
        "transform",
        `translate(${newMargin.left}, ${300 + newMargin.top})`
      );
    });
  });
});
