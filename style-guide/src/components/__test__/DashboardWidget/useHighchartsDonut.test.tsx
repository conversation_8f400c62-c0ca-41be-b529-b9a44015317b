import { renderHook } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useHighchartsDonut } from '../../DashboardWidget/hooks/useHighchartsDonut';

describe('useHighchartsDonut (real implementation)', () => {
  const baseData = [
    { label: 'A', value: 10, color: '#111', url: '/a' },
    { label: 'B', value: 20, color: '#222', url: '/b' },
  ];

  test('builds Highcharts options with mapping and size propagation', () => {
    const { result } = renderHook(() => useHighchartsDonut({ data: baseData, size: 300, showLabels: true }));
    const { options, tooltipStyle } = result.current;

    expect(options.chart?.type).toBe('pie');
    expect(options.series && (options.series as any)[0].data).toEqual([
      { name: 'A', y: 10, color: '#111', url: '/a' },
      { name: 'B', y: 20, color: '#222', url: '/b' },
    ]);

    // legend and plotOptions presence
    expect(options.plotOptions?.pie?.size).toBe(300);
    expect(typeof tooltipStyle).toBe('string');
  });

  test('tooltip positioner branches: x left/right and y clamping top/bottom', () => {
    const { result } = renderHook(() => useHighchartsDonut({ data: baseData, showLabels: false }));
    const tooltip = result.current.options.tooltip as any;

    // Case 1: large plotX triggers left placement (no arrow-left class), y clamped to 5
    const ctx1: any = {
      chart: { plotWidth: 400, plotHeight: 200 },
      label: { removeClass: jest.fn(), addClass: jest.fn() },
    };
    const pos1 = tooltip.positioner.call(ctx1, 100, 40, { plotX: 350, plotY: 0 });
    expect(pos1.x).toBe(350 - 100 - 15);
    expect(pos1.y).toBe(5);
    expect(ctx1.label.addClass).not.toHaveBeenCalledWith('arrow-left');

    // Case 2: small plotX triggers right placement and adds arrow-left class, y clamped to bottom
    const ctx2: any = {
      chart: { plotWidth: 300, plotHeight: 100 },
      label: { removeClass: jest.fn(), addClass: jest.fn() },
    };
    const pos2 = tooltip.positioner.call(ctx2, 120, 50, { plotX: 10, plotY: 200 });
    expect(pos2.x).toBe(10 + 15);
    expect(pos2.y).toBe(100 - 50 - 5);
    expect(ctx2.label.addClass).toHaveBeenCalledWith('arrow-left');
  });

  test('click handler is callable when url is present', () => {
    const { result } = renderHook(() => useHighchartsDonut({ data: baseData, showLabels: false }));
    const plotOptions = result.current.options.plotOptions as any;

    const point: any = { options: { url: '/somewhere' } };
    expect(() => {
      plotOptions.pie.point.events.click.call(point);
    }).not.toThrow();
  });

  test('click handler does nothing when url is missing', () => {
    const { result } = renderHook(() => useHighchartsDonut({ data: baseData, showLabels: false }));
    const plotOptions = result.current.options.plotOptions as any;

    const point: any = { options: {} };
    expect(() => plotOptions.pie.point.events.click.call(point)).not.toThrow();
  });

  test('memo updates when data or size change', () => {
    const { result, rerender } = renderHook(
      ({ dt, size }) => useHighchartsDonut({ data: dt, size, showLabels: false }),
      { initialProps: { dt: baseData, size: 200 } }
    );

    const first = result.current.options;
    rerender({ dt: [...baseData, { label: 'C', value: 5, color: '#333', url: '/c' }], size: 220 });
    const second = result.current.options;

    expect((first.series as any)[0].data).toHaveLength(2);
    expect((second.series as any)[0].data).toHaveLength(3);
    expect((second.plotOptions as any).pie.size).toBe(220);
  });
});
