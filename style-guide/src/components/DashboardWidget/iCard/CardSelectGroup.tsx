import React, { useEffect } from "react";
import { CardDropdownSelectGroupProps } from "../types/card-types";
import { CardDropdown } from "./CardDropdown";
import "./styles/CardSelectGroup.scss";
import classNames from "classnames";
import { getGACategory } from "../util/util";

/**
 * A simple wrapper component for the VesselDropdown.
 * Wrapped with React.memo because it's a pure component.
 * It will only re-render if its props change.
 */
export const CardDropdownSelectGroup: React.FC<CardDropdownSelectGroupProps> = React.memo(
  ({
    index,
    config,
    selectedItems,
    groups,
    onChange,
    isSearchBoxVisible,
    isSelectAllVisible,
    ga4EventTrigger,
    configKey,
  }) => {

    useEffect(() => {
      if (selectedItems.length > 0 && typeof ga4EventTrigger === "function") {
        const selectedNames = selectedItems.join(", ");
        const gaCategory = getGACategory(configKey);

        if (gaCategory) {
          if (index === 0) {
            ga4EventTrigger(gaCategory, selectedNames, "Vessel Selection");
          } else if (index === 1) {
            ga4EventTrigger(gaCategory, selectedNames, "Level of RA Selection");
          }
        } else {
          console.error("Unknown widget configKey:", configKey);
        }
      }
    }, [selectedItems, index, ga4EventTrigger, configKey]);

    // Move the hook to the top, before any conditionals
    const handleSelectionChange = React.useCallback(
      (newSelected: readonly string[]) => {
        onChange(index, newSelected as string[]);
      },
      [index, onChange]
    );

    if (!groups) {
      console.error("VesselSelectGroup: `groups` prop is missing.");
      return null;
    }
    const getWidthClass = (width: string | undefined) => {
      switch (width) {
        case "200px":
          return "dropdownWidthSmall";
        case "300px":
          return "dropdownWidthMedium";
        case "350px":
          return "dropdownWidthLarge";
        default:
          return "";
      }
    };

    return (
      <div
        className={classNames("raSelectWrapper", getWidthClass(config.width))}
      >
        <CardDropdown
          groups={groups}
          selectedItems={selectedItems}
          onSelectionChange={handleSelectionChange}
          placeholder={config.placeholder}
          width={config.width ?? "200px"}
          isSearchBoxVisible={isSearchBoxVisible}
          isSelectAllVisible={isSelectAllVisible}
        />
      </div>
    );
  }
);
