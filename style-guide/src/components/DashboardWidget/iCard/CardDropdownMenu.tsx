import React from "react";
import { Search } from "lucide-react";
import {
  CardDropdownMenuProps,
  DropdownFooterProps,
  GroupListProps,
  SearchInputProps,
} from "../types/card-types";
import "./styles/CardDropdown.scss";
import { RaCheckedIcon, RaUncheckedIcon } from "./svgIcons";

// --- Search Input Sub-component ---

const SearchInput: React.FC<SearchInputProps> = React.memo(
  ({ searchTerm, onSearchChange }) => (
    <div className="raSearchContainer">
      <Search size={16} className="raSearchIcon" />
      <input
        type="text"
        placeholder="Search"
        value={searchTerm}
        onChange={(e) => onSearchChange(e.target.value)}
        className="raSearchInput"
        aria-label="Search vessels"
      />
    </div>
  )
);

// --- Vessel List Sub-component ---

const GroupList: React.FC<GroupListProps> = ({
  filteredGroups,
  selectedItems,
  onToggleVessel,
  onToggleGroup,
}) => (
  <div className="raVesselGroups">
    {filteredGroups.map((group, groupIndex) => (
      <div key={`group-${group.id}`} className="raVesselGroup">
        <ul className="raVesselList">
          {group.vessels.map((vessel) => (
            <li
            key={vessel.vessel_id}
             // it is not required but need to check some edgecases by seeding more data in vesselData, after commit stil everything working fine
            //   key={`vessel-${group?.id}-${vessel?.vessel_id}-${vessel?.vessel_ownership_id}`}
              className="raVesselItem"
            >
              <label>
                <input
                  type="checkbox"
                  checked={selectedItems.includes(vessel.name)}
                  onChange={() => onToggleVessel(vessel.name)}
                  className="raCheckbox"
                  name="vessel-box"
                />
                <div className="raCheckboxIcon">
                  {selectedItems.includes(vessel.name) ? (
                    <RaCheckedIcon />
                  ) : (
                    <RaUncheckedIcon />
                  )}
                </div>
                {vessel.name}
              </label>
            </li>
          ))}
        </ul>
      </div>
    ))}
  </div>
);

// --- Footer Sub-component ---

const DropdownFooter: React.FC<DropdownFooterProps> = React.memo(
  ({ isAllSelected, onToggleAll }) => (
    <div className="raSelectAllContainer">
      <button onClick={onToggleAll} className="raSelectAlltext">
        <span>{`${isAllSelected ? "Unselect All" : "Select All"}`}</span>
      </button>
    </div>
  )
);

// --- Main Menu Component (Composer) ---

export const CardDropdownMenu: React.FC<CardDropdownMenuProps> = (props) => (
  <div className="raDropdownMenu">
    {props.isSearchBoxVisible && (
      <SearchInput
        searchTerm={props.searchTerm}
        onSearchChange={props.onSearchChange}
      />
    )}

    <GroupList {...props} />

    {props.isSelectAllVisible && (
      <DropdownFooter
        isAllSelected={props.isAllSelected}
        onToggleAll={props.onToggleAll}
      />
    )}
  </div>
);
