import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import { CardTabs } from "../../DashboardWidget/iCard/CardTabs";

const mockProps = {
  tabs: ["Active", "Inactive"],
  activeTab: "Active",
  onTabChange: jest.fn(),
  IsAllTabVisible: true,
};

describe("CardTabs Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("renders without crashing", () => {
    render(<CardTabs {...mockProps} />);
    expect(screen.getByText("All")).toBeInTheDocument(); // All tab is added when IsAllTabVisible is true
    expect(screen.getByText("Active")).toBeInTheDocument();
    expect(screen.getByText("Inactive")).toBeInTheDocument();
  });

  test("renders all provided tabs with All tab when IsAllTabVisible is true", () => {
    render(<CardTabs {...mockProps} />);

    expect(screen.getByText("All")).toBeInTheDocument();
    expect(screen.getByText("Active")).toBeInTheDocument();
    expect(screen.getByText("Inactive")).toBeInTheDocument();
  });

  test("highlights the active tab", () => {
    render(<CardTabs {...mockProps} />);

    const activeTab = screen.getByText("Active");
    expect(activeTab).toBeInTheDocument();

    // Active tab should have active styling (this depends on implementation)
    expect(activeTab.closest("button")).toHaveClass("active");
  });

  test("calls onTabChange when a tab is clicked", () => {
    render(<CardTabs {...mockProps} />);

    const inactiveTab = screen.getByText("Inactive");
    fireEvent.click(inactiveTab);

    expect(mockProps.onTabChange).toHaveBeenCalledWith("Inactive");
  });

  test("calls onTabChange when All tab is clicked", () => {
    render(<CardTabs {...mockProps} />);

    const allTab = screen.getByText("All");
    fireEvent.click(allTab);

    expect(mockProps.onTabChange).toHaveBeenCalledWith("All");
  });

  test("calls onTabChange when active tab is clicked", () => {
    render(<CardTabs {...mockProps} />);

    const activeTab = screen.getByText("Active");
    fireEvent.click(activeTab);

    // Component calls onTabChange even for active tab
    expect(mockProps.onTabChange).toHaveBeenCalledWith("Active");
  });

  test("handles different active tab", () => {
    const propsWithDifferentActive = {
      ...mockProps,
      activeTab: "Inactive",
    };

    render(<CardTabs {...propsWithDifferentActive} />);

    const inactiveTab = screen.getByText("Inactive");
    expect(inactiveTab.closest("button")).toHaveClass("active");
  });

  test("applies correct container class", () => {
    const { container } = render(<CardTabs {...mockProps} />);

    const tabsContainer = container.querySelector(".ra-tabs-container");
    expect(tabsContainer).toBeInTheDocument();
  });

  test("renders without IsAllTabVisible", () => {
    const propsWithoutAll = { ...mockProps, IsAllTabVisible: false };
    render(<CardTabs {...propsWithoutAll} />);

    // Should not render All tab
    expect(screen.queryByText("All")).not.toBeInTheDocument();
    expect(screen.getByText("Active")).toBeInTheDocument();
    expect(screen.getByText("Inactive")).toBeInTheDocument();
  });

  test("renders with empty tabs array", () => {
    const propsWithEmptyTabs = {
      ...mockProps,
      tabs: [],
    };

    render(<CardTabs {...propsWithEmptyTabs} />);

    // Should render container but no tab buttons
    const { container } = render(<CardTabs {...propsWithEmptyTabs} />);
    const buttons = container.querySelectorAll("button");
    expect(buttons).toHaveLength(0);
  });

  test("renders with single tab", () => {
    const propsWithSingleTab = {
      ...mockProps,
      tabs: ["Only Tab"],
      activeTab: "Only Tab",
    };

    render(<CardTabs {...propsWithSingleTab} />);

    expect(screen.getByText("Only Tab")).toBeInTheDocument();
    expect(screen.getByText("Only Tab").closest("button")).toHaveClass(
      "active"
    );
  });

  test("handles tab with special characters", () => {
    const propsWithSpecialTabs = {
      ...mockProps,
      tabs: ["Tab-1", "Tab_2", "Tab 3"],
      activeTab: "Tab-1",
    };

    render(<CardTabs {...propsWithSpecialTabs} />);

    expect(screen.getByText("Tab-1")).toBeInTheDocument();
    expect(screen.getByText("Tab_2")).toBeInTheDocument();
    expect(screen.getByText("Tab 3")).toBeInTheDocument();
  });

  test("maintains tab order with All first", () => {
    render(<CardTabs {...mockProps} />);

    const buttons = screen.getAllByRole("button");
    expect(buttons[0]).toHaveTextContent("All");
    expect(buttons[1]).toHaveTextContent("Active");
    expect(buttons[2]).toHaveTextContent("Inactive");
  });

  test("handles missing onTabChange callback", () => {
    const propsWithoutCallback = {
      ...mockProps,
      onTabChange: jest.fn(), // Provide a mock function instead of undefined
    };

    // Should render without crashing
    expect(() => {
      render(<CardTabs {...propsWithoutCallback} />);
    }).not.toThrow();
  });

  test("applies correct CSS classes to tabs", () => {
    const { container } = render(<CardTabs {...mockProps} />);

    const tabButtons = container.querySelectorAll("button");
    tabButtons.forEach((button) => {
      expect(button).toHaveClass("ra-tab-button");
    });

    // Active tab should have active class
    const activeButton = container.querySelector(".active");
    expect(activeButton).toBeInTheDocument();
  });
});
