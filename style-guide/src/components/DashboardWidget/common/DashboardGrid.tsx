import React from "react";
import { DeficiencyCountCard } from "./DeficiencyCountCard";
import { DonutChartCard } from "./DonutChartCard";
import { ChartData } from "../types/card-types";
import "./styles/DonutChartCard.scss";

interface DashboardGridProps {
  chartData: ChartData;
  department?: string;
  configKey?: string;
  ga4EventTrigger?: any;
  activeTab?: string;
}

export const DashboardGrid: React.FC<DashboardGridProps> = ({
  chartData,
  department,
  configKey,
  ga4EventTrigger,
  activeTab,
}) => {
  const { openDeficiencies, closedDeficiencies } = chartData;

  const dueDateData = {
    title: "Due Date",
    data: openDeficiencies.data, // Use data from openDeficiencies
  };

  const severityData = {
    title: "Severity",
    data: closedDeficiencies.data, // Use data from closedDeficiencies
  };

  return (
    <div className="dashboard-grid-container">
      <DeficiencyCountCard
        title={openDeficiencies.title}
        total={openDeficiencies.total}
      />
      <DeficiencyCountCard
        title={closedDeficiencies.title}
        total={closedDeficiencies.total}
      />
      <DonutChartCard
        title={dueDateData.title}
        data={dueDateData.data}
        department={department}
        configKey={configKey}
        ga4EventTrigger={ga4EventTrigger}
        activeTab={activeTab}
      />
      <DonutChartCard
        title={severityData.title}
        data={severityData.data}
        department={department}
        configKey={configKey}
        ga4EventTrigger={ga4EventTrigger}
        activeTab={activeTab}
      />
    </div>
  );
};
