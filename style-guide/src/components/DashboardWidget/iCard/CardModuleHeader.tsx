import React from "react";
import classNames from "classnames";
import { EnlargeIcon, List, LayoutGrid, Minimize2 } from "./svgIcons";
import "./styles/CardContainer.scss";
import { getGACategory } from "../util/util";

interface CardModuleHeaderProps {
  title: string;
  viewMode: "list" | "grid";
  isModal: boolean;
  IsiconRenderVisible?: boolean;
  IsenLargeIconVisible?: boolean;
  configKey?: string;
  ga4EventTrigger?: any;
  onViewModeChange: (mode: "list" | "grid") => void;
  onToggleModal: () => void;
}

export const CardModuleHeader: React.FC<CardModuleHeaderProps> = ({
  title,
  viewMode,
  isModal,
  IsiconRenderVisible = true,
  IsenLargeIconVisible = true,
  configKey,
  ga4EventTrigger,
  onViewModeChange,
  onToggleModal,
}) => {
  const handleViewModeChange = (mode: "list" | "grid") => {
    const gaCategory = getGACategory(configKey);
    if (ga4EventTrigger) {
      ga4EventTrigger(gaCategory, mode, "Switch View");
    }
    onViewModeChange(mode);
  };

  return (
    <div className="ra-vessel-module-header">
      <h2 className="ra-vessel-module-title">{title}</h2>
      <div className="ra-vessel-module-controls">
        {IsiconRenderVisible && (
          <div className="ra-view-toggle-container">
            <button
              onClick={() => handleViewModeChange("grid")}
              className={classNames("ra-view-toggle-button", {
                active: viewMode === "grid",
                "ra-view-toggle-button-modal": isModal,
              })}
              aria-label="Grid view"
            >
              <LayoutGrid className="ra-view-toggle-icon" />
            </button>
            <button
              onClick={() => handleViewModeChange("list")}
              className={classNames("ra-view-toggle-button", {
                active: viewMode === "list",
                "ra-view-toggle-button-modal": isModal,
              })}
              aria-label="List view"
            >
              <List className="ra-view-toggle-icon" />
            </button>
          </div>
        )}

        {IsenLargeIconVisible &&
          (isModal ? (
            <Minimize2
              className="ra-minimize-icon"
              onClick={onToggleModal}
              aria-label="Minimize view"
            />
          ) : (
            <EnlargeIcon
              className="ra-enlarge-icon"
              onClick={onToggleModal}
              aria-label="Enlarge view"
            />
          ))}
      </div>
    </div>
  );
};