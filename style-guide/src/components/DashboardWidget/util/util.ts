import { GA_Category, WidgetConstant } from "../types/widget.constant";

const filterMap: Record<string, string> = {
  Overdue: "overdue",
  "Due within 30 Days": "due30",
  "Due within 60 Days": "due60",
};

export function getSurveyActiveDueTab(
  value: string | null | undefined
): string {
  if (!value) return "";
  return filterMap[value] ?? "";
}

export const getGACategory = (configKey) => {
  switch (configKey) {
    case WidgetConstant.OWNER_FINANCIAL_REPORTING:
      return GA_Category.OWNER_FINANCIAL_REPORTING;
    case WidgetConstant.ITINERARY_ETA:
      return GA_Category.ITINERARY_ETA;
    case WidgetConstant.RISK_ASSESSMENT:
      return GA_Category.RISK_ASSESSMENT;
    case WidgetConstant.DEFICIENCIES:
      return GA_Category.DEFICIENCIES;
    case WidgetConstant.SURVEYS_CERTIFICATES:
      return GA_Category.SURVEYS_CERTIFICATES;
    default:
      return undefined;
  }
};

export const getPieChartRedirectionUrl = (
  label: string,
  activeTab: string | undefined,
  department: string | undefined
) => {
  if (!department) return "";
  if (!activeTab) return "";

  let url = `deficiency/list?acceptedByOffice=false&techGroup=${department}`;
  switch (label) {
    case "Overdue":
      url += "&overdue=true";
      break;
    case "Due within 30 Days":
      url += "&due=30d";
      break;
    case "Others":
      url += "&due=60d";
      break;
    case "High":
      url += "&severity=high";
      break;
    case "Medium":
      url += "&severity=medium";
      break;
    case "Low":
      url += "&severity=low";
      break;
    default:
      break;
  }

  switch (activeTab) {
    case "Defect":
      url += "&defectsOnly=true";
      break;
    case "Technical Follow-up":
      url += "&typeOfInspection=Technical Follow Up";
      break;
    default:
      break;
  }

  return url;
};

export function formatUTCDate(date) {
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    console.error("Invalid Date object passed to formatUTCDate");
    return "";
  }

  try {
    const day = date
      .getUTCDate()
      .toString()
      .padStart(2, "0");
    const month = date
      .toLocaleString("en-GB", {
        month: "short",
        timeZone: "UTC",
      })
      .replace(".", "");
    const year = date.getUTCFullYear();
    const hours = date
      .getUTCHours()
      .toString()
      .padStart(2, "0");
    const minutes = date
      .getUTCMinutes()
      .toString()
      .padStart(2, "0");
    const seconds = date
      .getUTCSeconds()
      .toString()
      .padStart(2, "0");

    return `${day} ${month} ${year} ${hours}:${minutes}:${seconds} UTC`;
  } catch (err) {
    console.error("Error formatting UTC date:", err);
    return "Error formatting date";
  }
}
